#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:10080"

def test_piclumen_with_auth():
    """测试PicLumen网站的认证"""
    print("=== 测试PicLumen认证 ===")
    
    # 你的认证token
    auth_token = "bdaf75d9ca454fc42d16db9fcfbed8984a208894"
    
    # 测试不同的认证方式
    auth_tests = [
        {
            "key": "piclumen_bearer",
            "auth_type": "Bearer",
            "description": "Bearer认证"
        },
        {
            "key": "piclumen_token", 
            "auth_type": "Token",
            "description": "Token认证"
        },
        {
            "key": "piclumen_apikey",
            "auth_type": "API-Key", 
            "description": "API-Key认证"
        },
        {
            "key": "piclumen_custom",
            "auth_type": "Custom",
            "description": "自定义认证"
        }
    ]
    
    for test in auth_tests:
        print(f"\n--- 测试: {test['description']} ---")
        
        source_data = {
            "key": test["key"],
            "url": "https://piclumen.com/app/image-generator/explore",
            "use_selenium": True,
            "auth_token": auth_token,
            "auth_type": test["auth_type"],
            "ttl": 3600
        }
        
        try:
            print(f"发送请求: {test['auth_type']} 认证")
            response = requests.post(
                f"{BASE_URL}/api/source/add",
                json=source_data,
                timeout=120  # 增加超时时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    print(f"✅ 解析成功!")
                    print(f"   标题: {data.get('title', 'N/A')[:50]}...")
                    print(f"   内容长度: {len(data.get('text', ''))} 字符")
                    print(f"   图片数量: {data.get('images_count', 0)}")
                    print(f"   认证类型: {data.get('auth_type', 'N/A')}")
                    print(f"   使用Selenium: {data.get('use_selenium', False)}")
                    
                    # 检查是否成功获取到图片
                    images = data.get('images', [])
                    if images:
                        print(f"   ✅ 成功获取图片!")
                        print(f"   前3张图片:")
                        for i, img_url in enumerate(images[:3]):
                            print(f"     {i+1}. {img_url[:80]}...")
                        
                        # 这个认证方式成功了，可以停止测试其他方式
                        print(f"\n🎉 {test['description']} 认证成功！")
                        return test["key"]
                    else:
                        print(f"   ⚠️  未获取到图片，可能仍需要登录")
                        
                    # 检查页面内容是否包含登录相关信息
                    text = data.get('text', '').lower()
                    if any(keyword in text for keyword in ['login', 'sign in', 'authenticate', '登录']):
                        print(f"   ⚠️  页面内容包含登录信息，认证可能失败")
                    else:
                        print(f"   ✅ 页面内容看起来正常")
                        
                else:
                    print(f"❌ 解析失败: {result['message']}")
                    
            else:
                print(f"❌ 请求失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        print("-" * 60)
        time.sleep(5)  # 等待一下再测试下一种方式
    
    return None

def test_search_in_piclumen():
    """测试在PicLumen规则中搜索"""
    print("\n=== 测试在PicLumen中搜索 ===")
    
    # 获取所有PicLumen相关的规则
    try:
        response = requests.get(f"{BASE_URL}/api/source/list", timeout=10)
        if response.status_code == 200:
            result = response.json()
            rules = result.get('rules', [])
            
            piclumen_rules = [rule for rule in rules if 'piclumen' in rule.get('key', '').lower()]
            
            if not piclumen_rules:
                print("❌ 没有找到PicLumen规则")
                return
            
            print(f"找到 {len(piclumen_rules)} 个PicLumen规则")
            
            # 选择第一个规则进行搜索测试
            rule_key = piclumen_rules[0]['key']
            print(f"使用规则: {rule_key}")
            
            # 测试搜索
            search_tests = [
                {"keyword": "AI", "rule_key": rule_key},
                {"keyword": "art", "rule_key": rule_key},
                {"keyword": "image", "rule_key": rule_key}
            ]
            
            for test in search_tests:
                print(f"\n搜索: '{test['keyword']}' 在规则 '{test['rule_key']}' 中")
                
                try:
                    response = requests.post(
                        f"{BASE_URL}/api/search/rule",
                        json=test,
                        timeout=30
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"✅ 搜索成功: {result['message']}")
                        print(f"   状态: {result.get('status')}")
                        print(f"   来源缓存: {result.get('from_cache', False)}")
                        
                        if result.get('status') == 'completed':
                            results = result.get('results', [])
                            print(f"   结果数量: {len(results)}")
                            
                            for i, item in enumerate(results):
                                print(f"   结果 {i+1}:")
                                print(f"     标题: {item.get('title', 'N/A')[:50]}...")
                                print(f"     内容: {item.get('content', 'N/A')[:100]}...")
                                print(f"     匹配数: {item.get('match_count', 0)}")
                                
                    else:
                        result = response.json()
                        print(f"❌ 搜索失败: {result.get('message', response.text)}")
                        
                except Exception as e:
                    print(f"❌ 搜索异常: {e}")
                
                time.sleep(2)
                
        else:
            print(f"❌ 获取规则列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取规则列表异常: {e}")

def test_manual_browser_check():
    """提供手动浏览器检查的建议"""
    print("\n=== 手动检查建议 ===")
    print("如果自动认证失败，请尝试以下步骤:")
    print("1. 在浏览器中打开: https://piclumen.com/app/image-generator/explore")
    print("2. 检查是否需要登录")
    print("3. 如果需要登录，检查登录后的认证方式:")
    print("   - 查看Cookie中的认证信息")
    print("   - 查看localStorage中的token")
    print("   - 查看网络请求中的认证头")
    print("4. 根据实际的认证方式调整auth_type参数")
    print("\n常见认证方式:")
    print("- Cookie认证: 查看登录后的Cookie")
    print("- JWT Token: 通常在localStorage或Authorization头中")
    print("- API Key: 通常在X-API-Key头中")
    print("- Session: 通常在Cookie中的session_id")

def main():
    """主测试函数"""
    print("开始测试PicLumen认证...")
    print("=" * 80)
    
    # 健康检查
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器健康检查通过")
        else:
            print("❌ 服务器健康检查失败")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 测试PicLumen认证
    successful_rule = test_piclumen_with_auth()
    
    if successful_rule:
        # 如果有成功的认证，测试搜索功能
        test_search_in_piclumen()
    else:
        print("\n❌ 所有认证方式都失败了")
        test_manual_browser_check()
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("注意:")
    print("1. PicLumen可能使用复杂的认证机制")
    print("2. 可能需要先在浏览器中登录获取正确的认证信息")
    print("3. 认证token可能有时效性或域名限制")

if __name__ == "__main__":
    main()
