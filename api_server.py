#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import json
import os
import threading
import time
from datetime import datetime
from news.cache_manager import cache_manager
from news.source_manager import source_manager

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 所有任务状态都存储在Redis缓存中，不再使用本地内存


def run_scrapy_spider(task_id, keyword, source):
    """在后台运行Scrapy爬虫，将结果写入缓存"""
    start_time = datetime.now()

    # 使用关键字作为任务key，防止重复请求
    keyword_task_key = f"keyword_task:{keyword}"

    # 将任务状态写入缓存（使用关键字key）
    task_info = {
        'task_id': task_id,
        'keyword': keyword,
        'source': source,
        'status': 'running',
        'start_time': start_time.isoformat(),
        'end_time': None,
        'results': [],
        'error_message': None
    }
    cache_manager.set_task_status(keyword_task_key, task_info)
    # 同时也保存一份以task_id为key的记录，用于兼容现有查询
    cache_manager.set_task_status(task_id, task_info)

    try:
        # 构建Scrapy命令
        cmd = [
            'scrapy', 'crawl', 'keyword_search',
            '-a', f'keyword={keyword}',
            '-a', f'source={source}',
            '-s', 'LOG_LEVEL=WARNING'  # 减少日志输出
        ]

        # 运行爬虫
        result = subprocess.run(
            cmd,
            cwd='.',  # 在当前目录运行
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )

        end_time = datetime.now()

        if result.returncode == 0:
            # 爬虫成功完成，结果已经在爬虫中直接写入缓存
            try:
                # 检查缓存中是否有结果
                cached_results = cache_manager.get_search_results(keyword, source)
                results_count = len(cached_results) if cached_results else 0

                # 更新任务状态为完成
                task_info.update({
                    'status': 'completed',
                    'end_time': end_time.isoformat(),
                    'results_count': results_count,
                    'cache_key': keyword  # 记录缓存键
                })

                print(f"任务完成: {keyword} - {results_count} 条结果已缓存")
                # 更新关键字任务状态和task_id任务状态
                cache_manager.set_task_status(keyword_task_key, task_info)
                cache_manager.set_task_status(task_id, task_info)

            except Exception as e:
                print(f"处理任务完成状态失败: {e}")
                task_info.update({
                    'status': 'failed',
                    'end_time': end_time.isoformat(),
                    'error_message': f'处理任务完成状态失败: {str(e)}'
                })
                cache_manager.set_task_status(keyword_task_key, task_info)
                cache_manager.set_task_status(task_id, task_info)

        else:
            # 爬虫失败
            task_info.update({
                'status': 'failed',
                'end_time': end_time.isoformat(),
                'error_message': result.stderr
            })
            cache_manager.set_task_status(keyword_task_key, task_info)
            cache_manager.set_task_status(task_id, task_info)

    except subprocess.TimeoutExpired:
        task_info.update({
            'status': 'failed',
            'end_time': datetime.now().isoformat(),
            'error_message': '爬虫执行超时'
        })
        cache_manager.set_task_status(keyword_task_key, task_info)
        cache_manager.set_task_status(task_id, task_info)
    except Exception as e:
        task_info.update({
            'status': 'failed',
            'end_time': datetime.now().isoformat(),
            'error_message': str(e)
        })
        cache_manager.set_task_status(keyword_task_key, task_info)
        cache_manager.set_task_status(task_id, task_info)


def load_crawl_results(keyword, start_time):
    """从pipeline获取爬虫结果"""
    # 由于我们不再保存本地文件，这里需要从其他方式获取结果
    # 实际上，结果会在爬虫执行过程中通过pipeline收集
    # 这个函数现在主要是为了兼容性，实际结果获取在爬虫内部处理

    # 尝试从临时存储获取结果（如果有的话）
    try:
        # 这里可以实现从全局变量或其他临时存储获取结果的逻辑
        # 目前返回空列表，实际结果会在爬虫pipeline中处理
        return []
    except Exception as e:
        print(f"获取爬虫结果失败: {e}")
        return []


def run_rule_based_spider(task_id, keyword, rule_key, rule_data):
    """运行基于规则的爬虫任务"""
    start_time = datetime.now()
    cache_key = f"{keyword}_{rule_key}"

    # 使用关键字+规则作为任务key，防止重复请求
    keyword_task_key = f"keyword_task:{cache_key}"

    # 将任务状态写入缓存
    task_info = {
        'task_id': task_id,
        'keyword': keyword,
        'rule_key': rule_key,
        'source': rule_key,
        'status': 'running',
        'start_time': start_time.isoformat(),
        'end_time': None,
        'results': [],
        'error_message': None
    }
    cache_manager.set_task_status(keyword_task_key, task_info)
    cache_manager.set_task_status(task_id, task_info)

    try:
        # 基于规则进行搜索（在规则数据中搜索关键字）
        search_result = source_manager.search_by_keyword_and_rule(keyword, rule_key)

        end_time = datetime.now()

        if search_result['success']:
            results = search_result.get('results', [])

            # 将结果写入缓存（使用关键字+规则作为缓存key）
            if results:
                cache_success = cache_manager.set_search_results(cache_key, results, rule_key)
                if not cache_success:
                    print(f"警告: 缓存写入失败 - {cache_key}")

            # 更新任务状态为完成
            task_info.update({
                'status': 'completed',
                'end_time': end_time.isoformat(),
                'results_count': len(results),
                'cache_key': cache_key
            })

            print(f"基于规则的任务完成: {keyword} + {rule_key} - {len(results)} 条结果")
            cache_manager.set_task_status(keyword_task_key, task_info)
            cache_manager.set_task_status(task_id, task_info)

        else:
            # 搜索失败
            task_info.update({
                'status': 'failed',
                'end_time': end_time.isoformat(),
                'error_message': search_result.get('message', '搜索失败')
            })
            cache_manager.set_task_status(keyword_task_key, task_info)
            cache_manager.set_task_status(task_id, task_info)

    except Exception as e:
        task_info.update({
            'status': 'failed',
            'end_time': datetime.now().isoformat(),
            'error_message': str(e)
        })
        cache_manager.set_task_status(keyword_task_key, task_info)
        cache_manager.set_task_status(task_id, task_info)











@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    cache_info = cache_manager.get_cache_info()

    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'cache_connected': cache_info['connected'],
        'cached_keywords': cache_info['search_keys']
    })


@app.route('/api/cache/clear', methods=['DELETE'])
def clear_cache():
    """清空所有缓存"""
    try:
        success = cache_manager.clear_all_cache()
        if success:
            return jsonify({'message': '缓存已清空'})
        else:
            return jsonify({'error': '清空缓存失败'}), 500
    except Exception as e:
        return jsonify({'error': f'清空缓存失败: {str(e)}'}), 500


# 数据源管理接口
@app.route('/api/source/add', methods=['POST'])
def add_source_rule():
    """添加数据源规则"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        key = data.get('key', '').strip()
        url = data.get('url', '').strip()
        ttl = data.get('ttl', 7200)  # 默认2小时
        use_selenium = data.get('use_selenium')  # None为自动检测
        auth_token = data.get('auth_token', '').strip() or None
        auth_type = data.get('auth_type', 'Bearer').strip()

        if not key:
            return jsonify({'error': '规则键名不能为空'}), 400

        if not url:
            return jsonify({'error': 'URL不能为空'}), 400

        # 验证URL格式
        if not url.startswith(('http://', 'https://')):
            return jsonify({'error': 'URL格式不正确'}), 400

        # 添加数据源规则
        result = source_manager.add_source_rule(key, url, ttl, use_selenium)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/source/list', methods=['GET'])
def list_source_rules():
    """获取所有数据源规则"""
    try:
        rules = source_manager.list_source_rules()
        return jsonify({
            'rules': rules,
            'count': len(rules)
        })
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/source/get/<key>', methods=['GET'])
def get_source_rule(key):
    """获取指定数据源规则"""
    try:
        rule = source_manager.get_source_rule(key)
        if rule:
            return jsonify(rule)
        else:
            return jsonify({'error': f'规则 "{key}" 不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/source/delete/<key>', methods=['DELETE'])
def delete_source_rule(key):
    """删除数据源规则"""
    try:
        success = source_manager.delete_source_rule(key)
        if success:
            return jsonify({'message': f'规则 "{key}" 已删除'})
        else:
            return jsonify({'error': f'删除规则 "{key}" 失败'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/search/rule', methods=['POST'])
def search_by_rule():
    """根据关键字和规则搜索，支持缓存优先和防重复"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        keyword = data.get('keyword', '').strip()
        rule_key = data.get('rule_key', '').strip()
        force_refresh = data.get('force_refresh', False)  # 是否强制刷新

        if not keyword:
            return jsonify({'error': '关键字不能为空'}), 400

        if not rule_key:
            return jsonify({'error': '规则键名不能为空'}), 400

        # 检查规则是否存在
        rule_data = source_manager.get_source_rule(rule_key)
        if not rule_data:
            return jsonify({'error': f'规则 "{rule_key}" 不存在'}), 404

        # 生成缓存键（关键字+规则键）
        cache_key = f"{keyword}_{rule_key}"

        # 首先检查缓存中是否已有结果
        if not force_refresh:
            cached_results = cache_manager.get_search_results(cache_key, rule_key)
            if cached_results:
                # 缓存中有结果，直接返回
                return jsonify({
                    'task_id': f"{cache_key}_cached_{int(time.time())}",
                    'message': '从缓存获取结果',
                    'keyword': keyword,
                    'rule_key': rule_key,
                    'source': rule_key,
                    'status': 'completed',
                    'results': cached_results,
                    'results_count': len(cached_results),
                    'from_cache': True,
                    'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

        # 检查是否有正在进行的任务（使用关键字+规则作为任务key）
        keyword_task_key = f"keyword_task:{cache_key}"
        existing_task = cache_manager.get_task_status(keyword_task_key)

        if existing_task:
            task_status = existing_task.get('status')

            if task_status == 'running':
                # 有正在进行的任务，返回现有任务信息
                return jsonify({
                    'task_id': existing_task.get('task_id'),
                    'message': '该关键字和规则的爬虫任务正在进行中，请稍后查询结果',
                    'keyword': keyword,
                    'rule_key': rule_key,
                    'source': rule_key,
                    'status': 'running',
                    'start_time': existing_task.get('start_time'),
                    'from_cache': False,
                    'is_duplicate_request': True
                })

            elif task_status == 'completed':
                # 任务已完成，检查缓存中是否有结果
                cached_results = cache_manager.get_search_results(cache_key, rule_key)
                if cached_results:
                    return jsonify({
                        'task_id': existing_task.get('task_id'),
                        'message': '任务已完成，从缓存获取结果',
                        'keyword': keyword,
                        'rule_key': rule_key,
                        'source': rule_key,
                        'status': 'completed',
                        'results': cached_results,
                        'results_count': len(cached_results),
                        'from_cache': True,
                        'start_time': existing_task.get('start_time'),
                        'end_time': existing_task.get('end_time')
                    })

            elif task_status == 'failed':
                # 上次任务失败，可以重新启动
                error_msg = existing_task.get('error_message', '未知错误')
                print(f"上次任务失败: {cache_key} - {error_msg}，将重新启动任务")
                # 继续执行新任务启动逻辑

        # 缓存中没有结果且没有正在进行的任务，启动新的爬虫任务
        task_id = f"{cache_key}_{int(time.time())}"

        # 在后台启动爬虫（使用规则对应的source和解析规则）
        thread = threading.Thread(
            target=run_rule_based_spider,
            args=(task_id, keyword, rule_key, rule_data)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'task_id': task_id,
            'message': '基于规则的爬虫任务已启动',
            'keyword': keyword,
            'rule_key': rule_key,
            'source': rule_key,
            'from_cache': False
        })

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/', methods=['GET'])
def index():
    """首页"""
    return jsonify({
        'message': '豆瓣电影关键字搜索爬虫API',
        'version': '2.0.0',
        'features': ['关键字搜索', 'Redis缓存', 'Selenium渲染'],
        'endpoints': {
            'POST /api/source/add': '添加数据源规则（使用BeautifulSoup解析）',
            'GET /api/source/list': '获取所有数据源规则',
            'GET /api/source/get/<key>': '获取指定数据源规则',
            'DELETE /api/source/delete/<key>': '删除数据源规则',
            'POST /api/search/rule': '根据关键字和规则搜索（缓存优先，防重复）',
            'GET /api/health': '健康检查',
            'DELETE /api/cache/clear': '清空所有缓存'
        },
        'cache_info': '缓存优先策略：如果关键字已缓存则直接返回结果，否则启动爬虫任务。支持force_refresh参数强制刷新。'
    })


if __name__ == '__main__':
    print("启动智能数据抓取API服务器...")
    print("特性: Redis缓存 + BeautifulSoup解析 + 智能防重复")
    print("API文档:")
    print("  POST /api/source/add - 添加数据源规则")
    print("    参数: {'key': '规则名', 'url': '网页URL', 'ttl': 7200}")
    print("  GET /api/source/list - 获取所有数据源规则")
    print("  POST /api/search/rule - 根据关键字和规则搜索")
    print("    参数: {'keyword': '关键字', 'rule_key': '规则名', 'force_refresh': false}")
    print("  GET /api/health - 健康检查")
    print("  DELETE /api/cache/clear - 清空缓存")
    print("\n智能策略:")
    print("  1. 有缓存 -> 直接返回缓存结果")
    print("  2. 无缓存但有进行中任务 -> 返回任务进行中状态")
    print("  3. 无缓存且无进行中任务 -> 启动基于规则的搜索任务")

    app.run(host='0.0.0.0', port=10080, debug=True)
