#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import json
import os
import threading
import time
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 存储爬虫任务状态
crawl_tasks = {}


class CrawlTask:
    """爬虫任务类"""
    def __init__(self, task_id, keyword, source='douban'):
        self.task_id = task_id
        self.keyword = keyword
        self.source = 'douban'  # 固定使用豆瓣
        self.status = 'running'  # running, completed, failed
        self.start_time = datetime.now()
        self.end_time = None
        self.results = []
        self.error_message = None


def run_scrapy_spider(task_id, keyword, source):
    """在后台运行Scrapy爬虫"""
    try:
        # 构建Scrapy命令
        cmd = [
            'scrapy', 'crawl', 'keyword_search',
            '-a', f'keyword={keyword}',
            '-a', f'source={source}',
            '-s', 'LOG_LEVEL=WARNING'  # 减少日志输出
        ]

        # 运行爬虫
        result = subprocess.run(
            cmd,
            cwd='.',  # 在当前目录运行
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )

        if result.returncode == 0:
            # 爬虫成功完成
            crawl_tasks[task_id].status = 'completed'
            crawl_tasks[task_id].end_time = datetime.now()

            # 尝试读取结果文件
            try:
                results = load_crawl_results(keyword, crawl_tasks[task_id].start_time)
                crawl_tasks[task_id].results = results
            except Exception as e:
                print(f"读取结果文件失败: {e}")

        else:
            # 爬虫失败
            crawl_tasks[task_id].status = 'failed'
            crawl_tasks[task_id].error_message = result.stderr
            crawl_tasks[task_id].end_time = datetime.now()

    except subprocess.TimeoutExpired:
        crawl_tasks[task_id].status = 'failed'
        crawl_tasks[task_id].error_message = '爬虫执行超时'
        crawl_tasks[task_id].end_time = datetime.now()
    except Exception as e:
        crawl_tasks[task_id].status = 'failed'
        crawl_tasks[task_id].error_message = str(e)
        crawl_tasks[task_id].end_time = datetime.now()


def load_crawl_results(keyword, start_time):
    """加载爬虫结果"""
    results_dir = 'search_results'
    if not os.path.exists(results_dir):
        return []

    # 查找对应的结果文件
    time_str = start_time.strftime('%Y-%m-%d_%H-%M-%S')
    pattern = f"search_{keyword}_{time_str}"

    for filename in os.listdir(results_dir):
        if filename.startswith(pattern) and filename.endswith('.json'):
            filepath = os.path.join(results_dir, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"读取文件 {filepath} 失败: {e}")
                return []

    return []


@app.route('/api/search', methods=['POST'])
def search():
    """关键字搜索接口"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        keyword = data.get('keyword', '').strip()
        source = 'douban'  # 固定使用豆瓣

        if not keyword:
            return jsonify({'error': '关键字不能为空'}), 400

        # 生成任务ID
        task_id = f"{keyword}_douban_{int(time.time())}"

        # 创建任务
        task = CrawlTask(task_id, keyword, source)
        crawl_tasks[task_id] = task

        # 在后台启动爬虫
        thread = threading.Thread(
            target=run_scrapy_spider,
            args=(task_id, keyword, source)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'task_id': task_id,
            'message': '爬虫任务已启动',
            'keyword': keyword,
            'source': source
        })

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/task/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态"""
    try:
        if task_id not in crawl_tasks:
            return jsonify({'error': '任务不存在'}), 404

        task = crawl_tasks[task_id]

        response = {
            'task_id': task_id,
            'keyword': task.keyword,
            'source': task.source,
            'status': task.status,
            'start_time': task.start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': task.end_time.strftime('%Y-%m-%d %H:%M:%S') if task.end_time else None,
            'results_count': len(task.results)
        }

        if task.status == 'failed' and task.error_message:
            response['error_message'] = task.error_message

        if task.status == 'completed':
            response['results'] = task.results

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/tasks', methods=['GET'])
def list_tasks():
    """获取所有任务列表"""
    try:
        tasks_list = []
        for task_id, task in crawl_tasks.items():
            tasks_list.append({
                'task_id': task_id,
                'keyword': task.keyword,
                'source': task.source,
                'status': task.status,
                'start_time': task.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': task.end_time.strftime('%Y-%m-%d %H:%M:%S') if task.end_time else None,
                'results_count': len(task.results)
            })

        return jsonify({'tasks': tasks_list})

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'active_tasks': len([t for t in crawl_tasks.values() if t.status == 'running'])
    })


@app.route('/', methods=['GET'])
def index():
    """首页"""
    return jsonify({
        'message': '关键字搜索爬虫API',
        'version': '1.0.0',
        'endpoints': {
            'POST /api/search': '启动关键字搜索任务',
            'GET /api/task/<task_id>': '获取任务状态和结果',
            'GET /api/tasks': '获取所有任务列表',
            'GET /api/health': '健康检查'
        }
    })


if __name__ == '__main__':
    print("启动关键字搜索爬虫API服务器...")
    print("API文档:")
    print("  POST /api/search - 启动搜索任务")
    print("    参数: {'keyword': '搜索关键字', 'source': 'baidu|bing|google'}")
    print("  GET /api/task/<task_id> - 获取任务状态")
    print("  GET /api/tasks - 获取所有任务")
    print("  GET /api/health - 健康检查")

    app.run(host='0.0.0.0', port=10080, debug=True)
