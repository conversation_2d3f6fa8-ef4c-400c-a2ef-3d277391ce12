#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import json
import os
import threading
import time
from datetime import datetime
from news.cache_manager import cache_manager
from news.source_manager import source_manager

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 所有任务状态都存储在Redis缓存中，不再使用本地内存


def run_scrapy_spider(task_id, keyword, source):
    """在后台运行Scrapy爬虫，将结果写入缓存"""
    start_time = datetime.now()

    # 使用关键字作为任务key，防止重复请求
    keyword_task_key = f"keyword_task:{keyword}"

    # 将任务状态写入缓存（使用关键字key）
    task_info = {
        'task_id': task_id,
        'keyword': keyword,
        'source': source,
        'status': 'running',
        'start_time': start_time.isoformat(),
        'end_time': None,
        'results': [],
        'error_message': None
    }
    cache_manager.set_task_status(keyword_task_key, task_info)
    # 同时也保存一份以task_id为key的记录，用于兼容现有查询
    cache_manager.set_task_status(task_id, task_info)

    try:
        # 构建Scrapy命令
        cmd = [
            'scrapy', 'crawl', 'keyword_search',
            '-a', f'keyword={keyword}',
            '-a', f'source={source}',
            '-s', 'LOG_LEVEL=WARNING'  # 减少日志输出
        ]

        # 运行爬虫
        result = subprocess.run(
            cmd,
            cwd='.',  # 在当前目录运行
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )

        end_time = datetime.now()

        if result.returncode == 0:
            # 爬虫成功完成，结果已经在爬虫中直接写入缓存
            try:
                # 检查缓存中是否有结果
                cached_results = cache_manager.get_search_results(keyword, source)
                results_count = len(cached_results) if cached_results else 0

                # 更新任务状态为完成
                task_info.update({
                    'status': 'completed',
                    'end_time': end_time.isoformat(),
                    'results_count': results_count,
                    'cache_key': keyword  # 记录缓存键
                })

                print(f"任务完成: {keyword} - {results_count} 条结果已缓存")
                # 更新关键字任务状态和task_id任务状态
                cache_manager.set_task_status(keyword_task_key, task_info)
                cache_manager.set_task_status(task_id, task_info)

            except Exception as e:
                print(f"处理任务完成状态失败: {e}")
                task_info.update({
                    'status': 'failed',
                    'end_time': end_time.isoformat(),
                    'error_message': f'处理任务完成状态失败: {str(e)}'
                })
                cache_manager.set_task_status(keyword_task_key, task_info)
                cache_manager.set_task_status(task_id, task_info)

        else:
            # 爬虫失败
            task_info.update({
                'status': 'failed',
                'end_time': end_time.isoformat(),
                'error_message': result.stderr
            })
            cache_manager.set_task_status(keyword_task_key, task_info)
            cache_manager.set_task_status(task_id, task_info)

    except subprocess.TimeoutExpired:
        task_info.update({
            'status': 'failed',
            'end_time': datetime.now().isoformat(),
            'error_message': '爬虫执行超时'
        })
        cache_manager.set_task_status(keyword_task_key, task_info)
        cache_manager.set_task_status(task_id, task_info)
    except Exception as e:
        task_info.update({
            'status': 'failed',
            'end_time': datetime.now().isoformat(),
            'error_message': str(e)
        })
        cache_manager.set_task_status(keyword_task_key, task_info)
        cache_manager.set_task_status(task_id, task_info)


def load_crawl_results(keyword, start_time):
    """从pipeline获取爬虫结果"""
    # 由于我们不再保存本地文件，这里需要从其他方式获取结果
    # 实际上，结果会在爬虫执行过程中通过pipeline收集
    # 这个函数现在主要是为了兼容性，实际结果获取在爬虫内部处理

    # 尝试从临时存储获取结果（如果有的话）
    try:
        # 这里可以实现从全局变量或其他临时存储获取结果的逻辑
        # 目前返回空列表，实际结果会在爬虫pipeline中处理
        return []
    except Exception as e:
        print(f"获取爬虫结果失败: {e}")
        return []











@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    cache_info = cache_manager.get_cache_info()

    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'cache_connected': cache_info['connected'],
        'cached_keywords': cache_info['search_keys']
    })


@app.route('/api/cache/clear', methods=['DELETE'])
def clear_cache():
    """清空所有缓存"""
    try:
        success = cache_manager.clear_all_cache()
        if success:
            return jsonify({'message': '缓存已清空'})
        else:
            return jsonify({'error': '清空缓存失败'}), 500
    except Exception as e:
        return jsonify({'error': f'清空缓存失败: {str(e)}'}), 500


# 数据源管理接口
@app.route('/api/source/add', methods=['POST'])
def add_source_rule():
    """添加数据源规则"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        key = data.get('key', '').strip()
        url = data.get('url', '').strip()
        ttl = data.get('ttl', 7200)  # 默认2小时

        if not key:
            return jsonify({'error': '规则键名不能为空'}), 400

        if not url:
            return jsonify({'error': 'URL不能为空'}), 400

        # 验证URL格式
        if not url.startswith(('http://', 'https://')):
            return jsonify({'error': 'URL格式不正确'}), 400

        # 添加数据源规则
        result = source_manager.add_source_rule(key, url, ttl)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/source/list', methods=['GET'])
def list_source_rules():
    """获取所有数据源规则"""
    try:
        rules = source_manager.list_source_rules()
        return jsonify({
            'rules': rules,
            'count': len(rules)
        })
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/source/get/<key>', methods=['GET'])
def get_source_rule(key):
    """获取指定数据源规则"""
    try:
        rule = source_manager.get_source_rule(key)
        if rule:
            return jsonify(rule)
        else:
            return jsonify({'error': f'规则 "{key}" 不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/source/delete/<key>', methods=['DELETE'])
def delete_source_rule(key):
    """删除数据源规则"""
    try:
        success = source_manager.delete_source_rule(key)
        if success:
            return jsonify({'message': f'规则 "{key}" 已删除'})
        else:
            return jsonify({'error': f'删除规则 "{key}" 失败'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/search/rule', methods=['POST'])
def search_by_rule():
    """根据关键字和规则搜索"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        keyword = data.get('keyword', '').strip()
        rule_key = data.get('rule_key', '').strip()

        if not keyword:
            return jsonify({'error': '关键字不能为空'}), 400

        if not rule_key:
            return jsonify({'error': '规则键名不能为空'}), 400

        # 执行搜索
        result = source_manager.search_by_keyword_and_rule(keyword, rule_key)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/', methods=['GET'])
def index():
    """首页"""
    return jsonify({
        'message': '豆瓣电影关键字搜索爬虫API',
        'version': '2.0.0',
        'features': ['关键字搜索', 'Redis缓存', 'Selenium渲染'],
        'endpoints': {
            'POST /api/search': '智能搜索（缓存优先，防重复请求）',
            'POST /api/source/add': '添加数据源规则（使用newspaper3k解析）',
            'GET /api/source/list': '获取所有数据源规则',
            'GET /api/source/get/<key>': '获取指定数据源规则',
            'DELETE /api/source/delete/<key>': '删除数据源规则',
            'POST /api/search/rule': '根据关键字和规则搜索',
            'GET /api/health': '健康检查',
            'DELETE /api/cache/clear': '清空所有缓存'
        },
        'cache_info': '缓存优先策略：如果关键字已缓存则直接返回结果，否则启动爬虫任务。支持force_refresh参数强制刷新。'
    })


if __name__ == '__main__':
    print("启动智能数据抓取API服务器...")
    print("特性: Redis缓存 + Selenium渲染 + Newspaper3k解析 + 智能防重复")
    print("API文档:")
    print("  POST /api/search - 豆瓣电影智能搜索")
    print("  POST /api/source/add - 添加数据源规则")
    print("    参数: {'key': '规则名', 'url': '网页URL', 'ttl': 7200}")
    print("  GET /api/source/list - 获取所有数据源规则")
    print("  POST /api/search/rule - 根据关键字和规则搜索")
    print("    参数: {'keyword': '关键字', 'rule_key': '规则名'}")
    print("  GET /api/health - 健康检查")
    print("  DELETE /api/cache/clear - 清空缓存")
    print("\n数据源管理:")
    print("  1. 使用newspaper3k自动解析网页内容")
    print("  2. 支持关键字在规则数据中搜索")
    print("  3. 灵活的缓存时间配置")

    app.run(host='0.0.0.0', port=10080, debug=True)
