#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import json
import os
import threading
import time
from datetime import datetime
from news.cache_manager import cache_manager

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 所有任务状态都存储在Redis缓存中，不再使用本地内存


def run_scrapy_spider(task_id, keyword, source):
    """在后台运行Scrapy爬虫，将结果写入缓存"""
    start_time = datetime.now()

    # 将任务状态写入缓存
    task_info = {
        'task_id': task_id,
        'keyword': keyword,
        'source': source,
        'status': 'running',
        'start_time': start_time.isoformat(),
        'end_time': None,
        'results': [],
        'error_message': None
    }
    cache_manager.set_task_status(task_id, task_info)

    try:
        # 构建Scrapy命令
        cmd = [
            'scrapy', 'crawl', 'keyword_search',
            '-a', f'keyword={keyword}',
            '-a', f'source={source}',
            '-s', 'LOG_LEVEL=WARNING'  # 减少日志输出
        ]

        # 运行爬虫
        result = subprocess.run(
            cmd,
            cwd='.',  # 在当前目录运行
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )

        end_time = datetime.now()

        if result.returncode == 0:
            # 爬虫成功完成，结果已经在爬虫中直接写入缓存
            try:
                # 检查缓存中是否有结果
                cached_results = cache_manager.get_search_results(keyword, source)
                results_count = len(cached_results) if cached_results else 0

                # 更新任务状态为完成
                task_info.update({
                    'status': 'completed',
                    'end_time': end_time.isoformat(),
                    'results_count': results_count,
                    'cache_key': keyword  # 记录缓存键
                })

                print(f"任务完成: {keyword} - {results_count} 条结果已缓存")
                cache_manager.set_task_status(task_id, task_info)

            except Exception as e:
                print(f"处理任务完成状态失败: {e}")
                task_info.update({
                    'status': 'failed',
                    'end_time': end_time.isoformat(),
                    'error_message': f'处理任务完成状态失败: {str(e)}'
                })
                cache_manager.set_task_status(task_id, task_info)

        else:
            # 爬虫失败
            task_info.update({
                'status': 'failed',
                'end_time': end_time.isoformat(),
                'error_message': result.stderr
            })
            cache_manager.set_task_status(task_id, task_info)

    except subprocess.TimeoutExpired:
        task_info.update({
            'status': 'failed',
            'end_time': datetime.now().isoformat(),
            'error_message': '爬虫执行超时'
        })
        cache_manager.set_task_status(task_id, task_info)
    except Exception as e:
        task_info.update({
            'status': 'failed',
            'end_time': datetime.now().isoformat(),
            'error_message': str(e)
        })
        cache_manager.set_task_status(task_id, task_info)


def load_crawl_results(keyword, start_time):
    """从pipeline获取爬虫结果"""
    # 由于我们不再保存本地文件，这里需要从其他方式获取结果
    # 实际上，结果会在爬虫执行过程中通过pipeline收集
    # 这个函数现在主要是为了兼容性，实际结果获取在爬虫内部处理

    # 尝试从临时存储获取结果（如果有的话）
    try:
        # 这里可以实现从全局变量或其他临时存储获取结果的逻辑
        # 目前返回空列表，实际结果会在爬虫pipeline中处理
        return []
    except Exception as e:
        print(f"获取爬虫结果失败: {e}")
        return []


@app.route('/api/search', methods=['POST'])
def search():
    """关键字搜索接口"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        keyword = data.get('keyword', '').strip()
        source = 'douban'  # 固定使用豆瓣

        if not keyword:
            return jsonify({'error': '关键字不能为空'}), 400

        # 生成任务ID
        task_id = f"{keyword}_douban_{int(time.time())}"

        # 在后台启动爬虫（任务状态将在爬虫函数中写入缓存）
        thread = threading.Thread(
            target=run_scrapy_spider,
            args=(task_id, keyword, source)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'task_id': task_id,
            'message': '爬虫任务已启动',
            'keyword': keyword,
            'source': source
        })

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/task/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态，从缓存读取"""
    try:
        # 从缓存获取任务状态
        task_info = cache_manager.get_task_status(task_id)

        if task_info:
            response = {
                'task_id': task_id,
                'keyword': task_info.get('keyword'),
                'source': task_info.get('source'),
                'status': task_info.get('status'),
                'start_time': task_info.get('start_time'),
                'end_time': task_info.get('end_time'),
                'results_count': task_info.get('results_count', 0)
            }

            if task_info.get('status') == 'failed' and task_info.get('error_message'):
                response['error_message'] = task_info.get('error_message')

            if task_info.get('status') == 'completed':
                # 从关键字缓存中获取结果
                keyword = task_info.get('keyword')
                if keyword:
                    cached_results = cache_manager.get_search_results(keyword, 'douban')
                    response['results'] = cached_results or []
                    response['results_count'] = len(cached_results) if cached_results else 0
                else:
                    response['results'] = []

            return jsonify(response)

        # 任务不存在，尝试从task_id中提取关键字，然后从搜索结果缓存查找
        try:
            # 解析task_id格式: keyword_douban_timestamp
            parts = task_id.rsplit('_', 2)
            if len(parts) >= 2:
                keyword = parts[0]
                cached_results = cache_manager.get_search_results(keyword, 'douban')

                if cached_results:
                    return jsonify({
                        'task_id': task_id,
                        'keyword': keyword,
                        'source': 'douban',
                        'status': 'completed',
                        'start_time': 'unknown',
                        'end_time': 'unknown',
                        'results_count': len(cached_results),
                        'results': cached_results,
                        'from_cache': True,
                        'message': '从搜索结果缓存获取的历史结果'
                    })
        except Exception as e:
            pass

        return jsonify({'error': '任务不存在且缓存中无相关结果'}), 404

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/tasks', methods=['GET'])
def list_tasks():
    """获取所有任务列表，从缓存读取"""
    try:
        tasks_list = cache_manager.get_all_tasks()

        # 格式化任务列表
        formatted_tasks = []
        for task in tasks_list:
            # 获取结果数量
            results_count = task.get('results_count', 0)
            if results_count == 0 and task.get('status') == 'completed':
                # 如果任务状态中没有记录结果数量，尝试从关键字缓存获取
                keyword = task.get('keyword')
                if keyword:
                    cached_results = cache_manager.get_search_results(keyword, 'douban')
                    results_count = len(cached_results) if cached_results else 0

            formatted_tasks.append({
                'task_id': task.get('task_id'),
                'keyword': task.get('keyword'),
                'source': task.get('source'),
                'status': task.get('status'),
                'start_time': task.get('start_time'),
                'end_time': task.get('end_time'),
                'results_count': results_count
            })

        return jsonify({'tasks': formatted_tasks})

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    cache_info = cache_manager.get_cache_info()

    # 统计活跃任务数
    all_tasks = cache_manager.get_all_tasks()
    active_tasks = len([t for t in all_tasks if t.get('status') == 'running'])

    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'active_tasks': active_tasks,
        'total_tasks': len(all_tasks),
        'cache_connected': cache_info['connected'],
        'cached_keywords': cache_info['search_keys']
    })


@app.route('/api/cache/clear', methods=['DELETE'])
def clear_cache():
    """清空所有缓存"""
    try:
        success = cache_manager.clear_all_cache()
        if success:
            return jsonify({'message': '缓存已清空'})
        else:
            return jsonify({'error': '清空缓存失败'}), 500
    except Exception as e:
        return jsonify({'error': f'清空缓存失败: {str(e)}'}), 500


@app.route('/', methods=['GET'])
def index():
    """首页"""
    return jsonify({
        'message': '豆瓣电影关键字搜索爬虫API',
        'version': '2.0.0',
        'features': ['关键字搜索', 'Redis缓存', 'Selenium渲染'],
        'endpoints': {
            'POST /api/search': '启动关键字搜索任务',
            'GET /api/task/<task_id>': '获取任务状态和结果（支持缓存查询）',
            'GET /api/tasks': '获取所有任务列表',
            'GET /api/health': '健康检查',
            'DELETE /api/cache/clear': '清空所有缓存'
        },
        'cache_info': '任务完成后结果自动缓存2小时，任务查询API支持从缓存读取历史结果'
    })


if __name__ == '__main__':
    print("启动豆瓣电影关键字搜索爬虫API服务器...")
    print("特性: Redis缓存 + Selenium渲染")
    print("API文档:")
    print("  POST /api/search - 启动搜索任务")
    print("    参数: {'keyword': '电影名称'}")
    print("  GET /api/task/<task_id> - 获取任务状态（支持缓存查询）")
    print("  GET /api/tasks - 获取所有任务列表")
    print("  GET /api/health - 健康检查")
    print("  DELETE /api/cache/clear - 清空缓存")
    print("\n注意: 所有任务状态和结果都存储在Redis缓存中")

    app.run(host='0.0.0.0', port=10080, debug=True)
