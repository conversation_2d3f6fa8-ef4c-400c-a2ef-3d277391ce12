#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:5000"

def test_search_api():
    """测试搜索API"""
    print("=== 测试关键字搜索API ===")

    # 测试数据
    test_cases = [
        {"keyword": "复仇者联盟"},
        {"keyword": "肖申克的救赎"},
        {"keyword": "泰坦尼克号"}
    ]

    task_ids = []

    for test_case in test_cases:
        print(f"\n启动搜索任务: {test_case}")

        try:
            response = requests.post(
                f"{BASE_URL}/api/search",
                json=test_case,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 任务启动成功: {result['task_id']}")
                task_ids.append(result['task_id'])
            else:
                print(f"❌ 任务启动失败: {response.status_code} - {response.text}")

        except Exception as e:
            print(f"❌ 请求失败: {e}")

    return task_ids

def check_task_status(task_ids):
    """检查任务状态"""
    print("\n=== 检查任务状态 ===")

    for task_id in task_ids:
        try:
            response = requests.get(f"{BASE_URL}/api/task/{task_id}", timeout=10)

            if response.status_code == 200:
                result = response.json()
                print(f"\n任务 {task_id}:")
                print(f"  状态: {result['status']}")
                print(f"  关键字: {result['keyword']}")
                print(f"  搜索源: {result['source']}")
                print(f"  开始时间: {result['start_time']}")

                if result['status'] == 'completed':
                    print(f"  结果数量: {result['results_count']}")
                    if result.get('from_cache'):
                        print("  ✨ 结果来源: 缓存")
                    if result['results_count'] > 0:
                        print("  前3个结果:")
                        for i, item in enumerate(result['results'][:3]):
                            print(f"    {i+1}. {item['title'][:50]}...")
                elif result['status'] == 'failed':
                    print(f"  错误信息: {result.get('error_message', '未知错误')}")

            else:
                print(f"❌ 获取任务状态失败: {response.status_code}")

        except Exception as e:
            print(f"❌ 请求失败: {e}")

def test_health_check():
    """测试健康检查"""
    print("\n=== 健康检查 ===")

    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务健康状态: {result['status']}")
            print(f"   活跃任务数: {result['active_tasks']}")
            print(f"   检查时间: {result['timestamp']}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")

    except Exception as e:
        print(f"❌ 健康检查请求失败: {e}")

def test_list_tasks():
    """测试获取任务列表"""
    print("\n=== 获取任务列表 ===")

    try:
        response = requests.get(f"{BASE_URL}/api/tasks", timeout=10)

        if response.status_code == 200:
            result = response.json()
            tasks = result['tasks']
            print(f"✅ 共有 {len(tasks)} 个任务")

            for task in tasks:
                print(f"  - {task['task_id']}: {task['keyword']} ({task['status']})")

        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {e}")


def test_cache_functions():
    """测试缓存功能"""
    print("\n=== 测试缓存功能 ===")

    # 测试获取缓存信息
    try:
        response = requests.get(f"{BASE_URL}/api/cache/info", timeout=10)
        if response.status_code == 200:
            cache_info = response.json()
            print(f"✅ 缓存连接状态: {cache_info.get('connected')}")
            print(f"   总键数: {cache_info.get('total_keys')}")
            print(f"   搜索缓存数: {cache_info.get('search_keys')}")
            if cache_info.get('redis_info'):
                redis_info = cache_info['redis_info']
                print(f"   Redis版本: {redis_info.get('version')}")
                print(f"   内存使用: {redis_info.get('used_memory_human')}")
        else:
            print(f"❌ 获取缓存信息失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取缓存信息失败: {e}")

    # 测试获取缓存关键字列表
    try:
        response = requests.get(f"{BASE_URL}/api/cache/keywords", timeout=10)
        if response.status_code == 200:
            result = response.json()
            keywords = result.get('keywords', [])
            print(f"\n✅ 缓存的关键字 ({len(keywords)} 个):")
            for kw in keywords[:5]:  # 只显示前5个
                print(f"   - {kw.get('keyword')}: {kw.get('count')} 条结果, TTL: {kw.get('ttl')}秒")
        else:
            print(f"❌ 获取缓存关键字失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取缓存关键字失败: {e}")


def test_cache_clear():
    """测试清空缓存"""
    print("\n=== 测试清空缓存 ===")

    try:
        response = requests.delete(f"{BASE_URL}/api/cache/clear", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {result.get('message')}")
        else:
            print(f"❌ 清空缓存失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 清空缓存失败: {e}")

def main():
    """主测试函数"""
    print("开始测试豆瓣电影关键字搜索爬虫API（支持Redis缓存）...")

    # 健康检查
    test_health_check()

    # 测试缓存功能
    test_cache_functions()

    # 启动搜索任务
    task_ids = test_search_api()

    if task_ids:
        # 等待一段时间让任务开始执行
        print("\n⏳ 等待10秒让任务开始执行...")
        time.sleep(10)

        # 检查任务状态
        check_task_status(task_ids)

    # 获取任务列表
    test_list_tasks()

    # 再次测试缓存（应该有新的缓存数据）
    print("\n=== 任务完成后的缓存状态 ===")
    test_cache_functions()

    print("\n=== 测试完成 ===")
    print("注意：")
    print("1. 爬虫任务可能需要更长时间完成，请稍后再次检查任务状态")
    print("2. 缓存的搜索结果默认保存2小时")
    print("3. 可以使用 DELETE /api/cache/clear 清空所有缓存")

if __name__ == "__main__":
    main()
