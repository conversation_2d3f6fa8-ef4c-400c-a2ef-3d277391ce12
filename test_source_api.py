#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:10080"

def test_add_source_rule():
    """测试添加数据源规则"""
    print("=== 测试添加数据源规则 ===")
    
    # 测试数据
    test_sources = [
        {
            "key": "tech_news",
            "url": "https://techcrunch.com/2023/12/01/ai-breakthrough-news/",
            "ttl": 3600
        },
        {
            "key": "python_guide", 
            "url": "https://docs.python.org/3/tutorial/introduction.html",
            "ttl": 7200
        }
    ]
    
    for source in test_sources:
        print(f"\n添加数据源: {source['key']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/source/add",
                json=source,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 添加成功: {result['message']}")
                if 'data' in result:
                    data = result['data']
                    print(f"   标题: {data.get('title', 'N/A')[:50]}...")
                    print(f"   内容长度: {len(data.get('text', ''))} 字符")
                    print(f"   作者: {data.get('authors', [])}")
            else:
                print(f"❌ 添加失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")

def test_list_source_rules():
    """测试获取数据源规则列表"""
    print("\n=== 测试获取数据源规则列表 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/source/list", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            rules = result.get('rules', [])
            print(f"✅ 共有 {len(rules)} 个数据源规则")
            
            for rule in rules:
                print(f"  - {rule.get('key')}: {rule.get('title', 'N/A')[:30]}...")
                print(f"    URL: {rule.get('url')}")
                print(f"    剩余TTL: {rule.get('remaining_ttl', 0)} 秒")
                
        else:
            print(f"❌ 获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_get_source_rule():
    """测试获取指定数据源规则"""
    print("\n=== 测试获取指定数据源规则 ===")
    
    rule_key = "tech_news"
    
    try:
        response = requests.get(f"{BASE_URL}/api/source/get/{rule_key}", timeout=10)
        
        if response.status_code == 200:
            rule = response.json()
            print(f"✅ 获取规则成功: {rule_key}")
            print(f"   标题: {rule.get('title', 'N/A')}")
            print(f"   URL: {rule.get('url')}")
            print(f"   内容长度: {len(rule.get('text', ''))} 字符")
            print(f"   关键词: {rule.get('keywords', [])[:5]}")
        else:
            print(f"❌ 获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_search_by_rule():
    """测试根据关键字和规则搜索（新的缓存优先逻辑）"""
    print("\n=== 测试根据关键字和规则搜索 ===")

    # 测试搜索
    search_tests = [
        {"keyword": "AI", "rule_key": "tech_news"},
        {"keyword": "python", "rule_key": "python_guide"},
        {"keyword": "machine learning", "rule_key": "tech_news", "force_refresh": True}  # 强制刷新
    ]

    for test in search_tests:
        print(f"\n搜索: '{test['keyword']}' 在规则 '{test['rule_key']}' 中")
        if test.get('force_refresh'):
            print("  (强制刷新缓存)")

        try:
            response = requests.post(
                f"{BASE_URL}/api/search/rule",
                json=test,
                timeout=15
            )

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 搜索成功: {result['message']}")
                print(f"   任务ID: {result.get('task_id')}")
                print(f"   状态: {result.get('status', 'unknown')}")
                print(f"   来源缓存: {'是' if result.get('from_cache') else '否'}")

                if result.get('status') == 'completed':
                    results = result.get('results', [])
                    print(f"   结果数量: {len(results)}")
                    for i, item in enumerate(results[:2]):  # 只显示前2个
                        print(f"    结果 {i+1}:")
                        print(f"      标题: {item.get('title', 'N/A')[:50]}...")
                        print(f"      内容: {item.get('content', 'N/A')[:100]}...")
                        print(f"      匹配数: {item.get('match_count', 0)}")
                elif result.get('status') == 'running':
                    print("   任务正在进行中...")

            else:
                result = response.json()
                print(f"❌ 搜索失败: {result.get('message', response.text)}")

        except Exception as e:
            print(f"❌ 请求失败: {e}")

        # 等待一下再进行下一个测试
        time.sleep(2)

def test_delete_source_rule():
    """测试删除数据源规则"""
    print("\n=== 测试删除数据源规则 ===")
    
    rule_key = "test_rule"
    
    try:
        response = requests.delete(f"{BASE_URL}/api/source/delete/{rule_key}", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 删除成功: {result['message']}")
        else:
            print(f"❌ 删除失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_health_check():
    """测试健康检查"""
    print("=== 健康检查 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务健康状态: {result['status']}")
            print(f"   缓存连接: {'✅' if result.get('cache_connected') else '❌'}")
            print(f"   缓存关键字数: {result.get('cached_keywords', 0)}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 健康检查请求失败: {e}")

def main():
    """主测试函数"""
    print("开始测试数据源管理API...")
    print("=" * 50)
    
    # 健康检查
    test_health_check()
    
    # 添加数据源规则
    test_add_source_rule()
    
    # 等待处理完成
    print("\n⏳ 等待5秒让数据源处理完成...")
    time.sleep(5)
    
    # 获取规则列表
    test_list_source_rules()
    
    # 获取指定规则
    test_get_source_rule()
    
    # 根据规则搜索
    test_search_by_rule()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("注意:")
    print("1. 数据源规则使用newspaper3k自动解析网页")
    print("2. 支持灵活的TTL配置")
    print("3. 关键字搜索支持标题、内容、摘要匹配")

if __name__ == "__main__":
    main()
