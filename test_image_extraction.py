#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:10080"

def test_image_website_extraction():
    """测试图片网站的内容提取"""
    print("=== 测试图片网站内容提取 ===")
    
    # 测试不同类型的网站
    test_sites = [
        {
            "key": "piclumen_explore",
            "url": "https://piclumen.com/app/image-generator/explore",
            "use_selenium": True,  # 强制使用Selenium
            "description": "PicLumen图片生成器（JavaScript重度网站）"
        },
        {
            "key": "unsplash_photos",
            "url": "https://unsplash.com/",
            "use_selenium": None,  # 自动检测
            "description": "Unsplash图片网站"
        },
        {
            "key": "simple_html",
            "url": "https://httpbin.org/html",
            "use_selenium": False,  # 强制不使用Selenium
            "description": "简单HTML页面（测试对比）"
        }
    ]
    
    for site in test_sites:
        print(f"\n--- 测试: {site['description']} ---")
        print(f"URL: {site['url']}")
        print(f"Selenium: {site['use_selenium']}")
        
        # 添加数据源规则
        source_data = {
            "key": site["key"],
            "url": site["url"],
            "ttl": 3600,
            "use_selenium": site["use_selenium"]
        }
        
        try:
            print("正在解析网页...")
            response = requests.post(
                f"{BASE_URL}/api/source/add",
                json=source_data,
                timeout=60  # 增加超时时间，因为Selenium需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    print(f"✅ 解析成功!")
                    print(f"   标题: {data.get('title', 'N/A')[:50]}...")
                    print(f"   内容长度: {len(data.get('text', ''))} 字符")
                    print(f"   图片数量: {data.get('images_count', 0)}")
                    print(f"   使用Selenium: {data.get('use_selenium', False)}")
                    
                    # 显示前几张图片URL
                    images = data.get('images', [])
                    if images:
                        print(f"   前3张图片:")
                        for i, img_url in enumerate(images[:3]):
                            print(f"     {i+1}. {img_url[:80]}...")
                    else:
                        print("   ⚠️  未找到图片")
                        
                    # 显示部分文本内容
                    text = data.get('text', '')
                    if text:
                        print(f"   内容预览: {text[:100]}...")
                    else:
                        print("   ⚠️  未提取到文本内容")
                        
                else:
                    print(f"❌ 解析失败: {result['message']}")
                    
            else:
                print(f"❌ 请求失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        print("-" * 60)
        time.sleep(2)  # 等待一下再处理下一个

def test_search_in_image_rules():
    """测试在图片规则中搜索"""
    print("\n=== 测试在图片规则中搜索 ===")
    
    # 测试搜索
    search_tests = [
        {"keyword": "image", "rule_key": "piclumen_explore"},
        {"keyword": "photo", "rule_key": "unsplash_photos"},
        {"keyword": "html", "rule_key": "simple_html"}
    ]
    
    for test in search_tests:
        print(f"\n搜索: '{test['keyword']}' 在规则 '{test['rule_key']}' 中")
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/search/rule",
                json=test,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 搜索成功: {result['message']}")
                print(f"   状态: {result.get('status')}")
                print(f"   来源缓存: {result.get('from_cache', False)}")
                
                if result.get('status') == 'completed':
                    results = result.get('results', [])
                    print(f"   结果数量: {len(results)}")
                    
                    for i, item in enumerate(results):
                        print(f"   结果 {i+1}:")
                        print(f"     标题: {item.get('title', 'N/A')[:50]}...")
                        print(f"     内容: {item.get('content', 'N/A')[:100]}...")
                        print(f"     匹配数: {item.get('match_count', 0)}")
                        print(f"     图片: {item.get('top_image', 'N/A')[:50]}...")
                        
            else:
                result = response.json()
                print(f"❌ 搜索失败: {result.get('message', response.text)}")
                
        except Exception as e:
            print(f"❌ 搜索异常: {e}")

def test_get_rule_details():
    """测试获取规则详情"""
    print("\n=== 测试获取规则详情 ===")
    
    rule_keys = ["piclumen_explore", "unsplash_photos", "simple_html"]
    
    for rule_key in rule_keys:
        print(f"\n获取规则: {rule_key}")
        
        try:
            response = requests.get(f"{BASE_URL}/api/source/get/{rule_key}", timeout=10)
            
            if response.status_code == 200:
                rule = response.json()
                print(f"✅ 获取成功")
                print(f"   标题: {rule.get('title', 'N/A')}")
                print(f"   URL: {rule.get('url')}")
                print(f"   图片数量: {len(rule.get('images', []))}")
                print(f"   使用Selenium: {rule.get('use_selenium', False)}")
                print(f"   提取时间: {rule.get('extracted_at', 'N/A')}")
                
                # 显示图片列表
                images = rule.get('images', [])
                if images:
                    print(f"   图片列表 (前5张):")
                    for i, img_url in enumerate(images[:5]):
                        print(f"     {i+1}. {img_url}")
                        
            else:
                print(f"❌ 获取失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 获取异常: {e}")

def main():
    """主测试函数"""
    print("开始测试图片网站内容提取...")
    print("=" * 80)
    
    # 健康检查
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器健康检查通过")
        else:
            print("❌ 服务器健康检查失败")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 测试图片网站提取
    test_image_website_extraction()
    
    # 等待一下让所有规则处理完成
    print("\n⏳ 等待10秒让所有规则处理完成...")
    time.sleep(10)
    
    # 测试获取规则详情
    test_get_rule_details()
    
    # 测试在图片规则中搜索
    test_search_in_image_rules()
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("分析结果:")
    print("1. 对于JavaScript重度网站（如PicLumen），需要使用Selenium")
    print("2. 对于传统网站，BeautifulSoup就足够了")
    print("3. 系统会自动检测并选择合适的解析方法")
    print("4. 图片提取支持多种属性和背景图片")

if __name__ == "__main__":
    main()
