#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import redis
import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)


class CacheManager:
    """Redis缓存管理器"""

    def __init__(self,
                 host: str = None,
                 port: int = None,
                 db: int = None,
                 password: str = None,
                 default_ttl: int = 7200):  # 默认2小时缓存
        """
        初始化缓存管理器

        Args:
            host: Redis主机地址
            port: Redis端口
            db: Redis数据库编号
            password: Redis密码
            default_ttl: 默认缓存时间（秒）
        """
        self.host = host or os.getenv('REDIS_HOST', 'localhost')
        self.port = port or int(os.getenv('REDIS_PORT', 6379))
        self.db = db or int(os.getenv('REDIS_DB', 0))
        self.password = password or os.getenv('REDIS_PASSWORD')
        self.default_ttl = default_ttl

        self.redis_client = None
        self._connect()

    def _connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            # 测试连接
            self.redis_client.ping()
            logger.info(f"Redis连接成功: {self.host}:{self.port}/{self.db}")
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}，将使用内存缓存")
            self.redis_client = None

    def _generate_cache_key(self, keyword: str, source: str = 'douban') -> str:
        """生成缓存键"""
        # 使用MD5哈希生成唯一键
        key_string = f"search:{source}:{keyword.lower()}"
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()

    def get_search_results(self, keyword: str, source: str = 'douban') -> Optional[List[Dict[str, Any]]]:
        """
        获取搜索结果缓存

        Args:
            keyword: 搜索关键字
            source: 搜索源

        Returns:
            缓存的搜索结果列表，如果没有缓存则返回None
        """
        if not self.redis_client:
            return None

        try:
            cache_key = self._generate_cache_key(keyword, source)
            cached_data = self.redis_client.get(cache_key)

            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"从缓存获取搜索结果: {keyword} ({len(data.get('results', []))} 条)")
                return data.get('results', [])

        except Exception as e:
            logger.error(f"获取缓存失败: {e}")

        return None

    def set_search_results(self,
                          keyword: str,
                          results: List[Dict[str, Any]],
                          source: str = 'douban',
                          ttl: int = None) -> bool:
        """
        设置搜索结果缓存

        Args:
            keyword: 搜索关键字
            results: 搜索结果列表
            source: 搜索源
            ttl: 缓存时间（秒），如果为None则使用默认值

        Returns:
            是否设置成功
        """
        if not self.redis_client:
            return False

        try:
            cache_key = self._generate_cache_key(keyword, source)
            cache_data = {
                'keyword': keyword,
                'source': source,
                'results': results,
                'cached_at': datetime.now().isoformat(),
                'count': len(results)
            }

            ttl = ttl or self.default_ttl
            success = self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(cache_data, ensure_ascii=False)
            )

            if success:
                logger.info(f"缓存搜索结果: {keyword} ({len(results)} 条，TTL: {ttl}秒)")
                return True

        except Exception as e:
            logger.error(f"设置缓存失败: {e}")

        return False

    def delete_search_cache(self, keyword: str, source: str = 'douban') -> bool:
        """
        删除指定关键字的缓存

        Args:
            keyword: 搜索关键字
            source: 搜索源

        Returns:
            是否删除成功
        """
        if not self.redis_client:
            return False

        try:
            cache_key = self._generate_cache_key(keyword, source)
            result = self.redis_client.delete(cache_key)

            if result:
                logger.info(f"删除缓存: {keyword}")
                return True

        except Exception as e:
            logger.error(f"删除缓存失败: {e}")

        return False

    def clear_all_cache(self) -> bool:
        """
        清空所有搜索缓存

        Returns:
            是否清空成功
        """
        if not self.redis_client:
            return False

        try:
            # 查找所有搜索相关的键
            keys = self.redis_client.keys("*")
            search_keys = [key for key in keys if key.startswith('search:') or len(key) == 32]  # MD5长度

            if search_keys:
                deleted = self.redis_client.delete(*search_keys)
                logger.info(f"清空缓存: 删除了 {deleted} 个键")
                return True
            else:
                logger.info("没有找到需要清空的缓存")
                return True

        except Exception as e:
            logger.error(f"清空缓存失败: {e}")

        return False

    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息

        Returns:
            缓存统计信息
        """
        info = {
            'connected': False,
            'total_keys': 0,
            'search_keys': 0,
            'memory_usage': 0,
            'redis_info': {}
        }

        if not self.redis_client:
            return info

        try:
            # 基本连接信息
            info['connected'] = True

            # 获取所有键
            all_keys = self.redis_client.keys("*")
            info['total_keys'] = len(all_keys)

            # 搜索相关的键
            search_keys = [key for key in all_keys if key.startswith('search:') or len(key) == 32]
            info['search_keys'] = len(search_keys)

            # Redis信息
            redis_info = self.redis_client.info()
            info['redis_info'] = {
                'version': redis_info.get('redis_version'),
                'used_memory': redis_info.get('used_memory'),
                'used_memory_human': redis_info.get('used_memory_human'),
                'connected_clients': redis_info.get('connected_clients'),
                'uptime_in_seconds': redis_info.get('uptime_in_seconds')
            }

        except Exception as e:
            logger.error(f"获取缓存信息失败: {e}")
            info['error'] = str(e)

        return info

    def get_cached_keywords(self) -> List[Dict[str, Any]]:
        """
        获取所有已缓存的关键字列表

        Returns:
            缓存的关键字信息列表
        """
        if not self.redis_client:
            return []

        try:
            all_keys = self.redis_client.keys("*")
            search_keys = [key for key in all_keys if len(key) == 32]  # MD5键

            cached_keywords = []
            for key in search_keys:
                try:
                    data = self.redis_client.get(key)
                    if data:
                        cache_info = json.loads(data)
                        ttl = self.redis_client.ttl(key)

                        cached_keywords.append({
                            'keyword': cache_info.get('keyword'),
                            'source': cache_info.get('source'),
                            'count': cache_info.get('count', 0),
                            'cached_at': cache_info.get('cached_at'),
                            'ttl': ttl if ttl > 0 else 0,
                            'cache_key': key
                        })
                except Exception as e:
                    logger.warning(f"解析缓存键 {key} 失败: {e}")
                    continue

            # 按缓存时间排序
            cached_keywords.sort(key=lambda x: x.get('cached_at', ''), reverse=True)
            return cached_keywords

        except Exception as e:
            logger.error(f"获取缓存关键字列表失败: {e}")
            return []

    def set_task_status(self, task_id: str, task_info: Dict[str, Any], ttl: int = None) -> bool:
        """
        设置任务状态信息

        Args:
            task_id: 任务ID
            task_info: 任务信息字典
            ttl: 缓存时间（秒），如果为None则使用默认值

        Returns:
            是否设置成功
        """
        if not self.redis_client:
            return False

        try:
            cache_key = f"task:{task_id}"
            ttl = ttl or self.default_ttl

            success = self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(task_info, ensure_ascii=False)
            )

            if success:
                logger.info(f"任务状态已缓存: {task_id} - {task_info.get('status')}")
                return True

        except Exception as e:
            logger.error(f"设置任务状态失败: {e}")

        return False

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态信息

        Args:
            task_id: 任务ID

        Returns:
            任务信息字典，如果没有找到则返回None
        """
        if not self.redis_client:
            return None

        try:
            cache_key = f"task:{task_id}"
            cached_data = self.redis_client.get(cache_key)

            if cached_data:
                task_info = json.loads(cached_data)
                logger.info(f"从缓存获取任务状态: {task_id} - {task_info.get('status')}")
                return task_info

        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")

        return None

    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务列表

        Returns:
            任务信息列表
        """
        if not self.redis_client:
            return []

        try:
            # 查找所有任务键
            task_keys = self.redis_client.keys("task:*")
            tasks = []

            for key in task_keys:
                try:
                    data = self.redis_client.get(key)
                    if data:
                        task_info = json.loads(data)
                        tasks.append(task_info)
                except Exception as e:
                    logger.warning(f"解析任务键 {key} 失败: {e}")
                    continue

            # 按开始时间排序
            tasks.sort(key=lambda x: x.get('start_time', ''), reverse=True)
            return tasks

        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return []

    def is_connected(self) -> bool:
        """检查Redis连接状态"""
        if not self.redis_client:
            return False

        try:
            self.redis_client.ping()
            return True
        except:
            return False


# 全局缓存管理器实例
cache_manager = CacheManager()
