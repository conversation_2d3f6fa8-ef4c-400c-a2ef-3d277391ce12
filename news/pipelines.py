# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import openpyxl
import json
import os
# useful for handling different item types with a single interface
from itemadapter import ItemAdapter
from news.items import NewsItem, SearchResultItem

class NewsPipeline:
    def __init__(self):
        self.wb = openpyxl.Workbook()
        self.sheet = self.wb.active
        self.sheet.title = 'Top250'
        self.sheet.append(('名称', '评分', '名言'))

    def process_item(self, item, spider):
        # 只处理NewsItem类型的数据
        if isinstance(item, NewsItem):
            self.sheet.append((item['title'], item['score'], item['motto']))
        return item

    def close_spider(self, spider):
        self.wb.save('豆瓣电影数据.xlsx')


class SearchResultPipeline:
    """搜索结果数据处理管道 - 只收集数据，不保存本地文件"""

    def __init__(self):
        self.results = []

    def process_item(self, item, spider):
        if isinstance(item, SearchResultItem):
            # 转换为字典格式
            item_dict = {
                'title': item.get('title', ''),
                'url': item.get('url', ''),
                'content': item.get('content', ''),
                'source': item.get('source', ''),
                'keyword': item.get('keyword', ''),
                'crawl_time': item.get('crawl_time', ''),
                'publish_time': item.get('publish_time', '')
            }
            self.results.append(item_dict)
        return item

    def close_spider(self, spider):
        if spider.name == 'keyword_search' and self.results:
            keyword = getattr(spider, 'keyword', 'unknown')
            print(f"爬取完成: {keyword} - 共 {len(self.results)} 条结果")
            print("结果将通过API缓存系统存储，不再保存本地文件")
