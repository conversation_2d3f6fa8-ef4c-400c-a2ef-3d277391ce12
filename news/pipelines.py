# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import openpyxl
import json
import os
# useful for handling different item types with a single interface
from itemadapter import ItemAdapter
from news.items import NewsItem, SearchResultItem

class NewsPipeline:
    def __init__(self):
        self.wb = openpyxl.Workbook()
        self.sheet = self.wb.active
        self.sheet.title = 'Top250'
        self.sheet.append(('名称', '评分', '名言'))

    def process_item(self, item, spider):
        # 只处理NewsItem类型的数据
        if isinstance(item, NewsItem):
            self.sheet.append((item['title'], item['score'], item['motto']))
        return item

    def close_spider(self, spider):
        self.wb.save('豆瓣电影数据.xlsx')


class SearchResultPipeline:
    """搜索结果数据处理管道"""

    def __init__(self):
        self.results = []
        self.output_dir = 'search_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def process_item(self, item, spider):
        if isinstance(item, SearchResultItem):
            # 转换为字典格式
            item_dict = {
                'title': item.get('title', ''),
                'url': item.get('url', ''),
                'content': item.get('content', ''),
                'source': item.get('source', ''),
                'keyword': item.get('keyword', ''),
                'crawl_time': item.get('crawl_time', ''),
                'publish_time': item.get('publish_time', '')
            }
            self.results.append(item_dict)
        return item

    def close_spider(self, spider):
        if spider.name == 'keyword_search' and self.results:
            # 保存为JSON文件
            keyword = getattr(spider, 'keyword', 'unknown')
            filename = f"{self.output_dir}/search_{keyword}_{spider.crawl_time.replace(':', '-').replace(' ', '_')}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)

            # 保存为Excel文件
            excel_filename = filename.replace('.json', '.xlsx')
            wb = openpyxl.Workbook()
            sheet = wb.active
            sheet.title = f'搜索结果_{keyword}'

            # 添加表头
            headers = ['标题', '链接', '内容摘要', '来源', '关键字', '爬取时间', '发布时间']
            sheet.append(headers)

            # 添加数据
            for result in self.results:
                row = [
                    result['title'],
                    result['url'],
                    result['content'],
                    result['source'],
                    result['keyword'],
                    result['crawl_time'],
                    result['publish_time']
                ]
                sheet.append(row)

            wb.save(excel_filename)

            print(f"搜索结果已保存到: {filename} 和 {excel_filename}")
            print(f"共爬取到 {len(self.results)} 条结果")
