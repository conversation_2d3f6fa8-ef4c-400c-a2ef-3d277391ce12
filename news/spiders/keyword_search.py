import scrapy
import requests
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import quote, urljoin
from scrapy.http import HtmlResponse
from news.items import SearchResultItem


class KeywordSearchSpider(scrapy.Spider):
    name = 'keyword_search'
    
    def __init__(self, keyword=None, source='baidu', *args, **kwargs):
        super(KeywordSearchSpider, self).__init__(*args, **kwargs)
        self.keyword = keyword
        self.source = source.lower()
        self.crawl_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if not self.keyword:
            raise ValueError("必须提供搜索关键字")
    
    def start_requests(self):
        """根据不同的搜索源生成请求"""
        if self.source == 'baidu':
            urls = self._get_baidu_urls()
        elif self.source == 'bing':
            urls = self._get_bing_urls()
        elif self.source == 'google':
            urls = self._get_google_urls()
        else:
            urls = self._get_baidu_urls()  # 默认使用百度
            
        for url in urls:
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                meta={'source': self.source}
            )
    
    def _get_baidu_urls(self):
        """生成百度搜索URL"""
        encoded_keyword = quote(self.keyword)
        urls = []
        for page in range(3):  # 搜索前3页
            url = f'https://www.baidu.com/s?wd={encoded_keyword}&pn={page * 10}'
            urls.append(url)
        return urls
    
    def _get_bing_urls(self):
        """生成Bing搜索URL"""
        encoded_keyword = quote(self.keyword)
        urls = []
        for page in range(3):  # 搜索前3页
            first = page * 10 + 1
            url = f'https://www.bing.com/search?q={encoded_keyword}&first={first}'
            urls.append(url)
        return urls
    
    def _get_google_urls(self):
        """生成Google搜索URL"""
        encoded_keyword = quote(self.keyword)
        urls = []
        for page in range(3):  # 搜索前3页
            start = page * 10
            url = f'https://www.google.com/search?q={encoded_keyword}&start={start}'
            urls.append(url)
        return urls
    
    def parse(self, response):
        """解析搜索结果页面"""
        source = response.meta.get('source', 'baidu')
        
        if source == 'baidu':
            yield from self._parse_baidu(response)
        elif source == 'bing':
            yield from self._parse_bing(response)
        elif source == 'google':
            yield from self._parse_google(response)
    
    def _parse_baidu(self, response):
        """解析百度搜索结果"""
        # 使用BeautifulSoup解析
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 百度搜索结果的选择器
        results = soup.find_all('div', class_='result')
        
        for result in results:
            item = SearchResultItem()
            
            # 提取标题和链接
            title_elem = result.find('h3')
            if title_elem:
                title_link = title_elem.find('a')
                if title_link:
                    item['title'] = title_link.get_text(strip=True)
                    item['url'] = title_link.get('href', '')
                else:
                    item['title'] = title_elem.get_text(strip=True)
                    item['url'] = ''
            else:
                continue
            
            # 提取内容摘要
            content_elem = result.find('span', class_='content-right_8Zs40')
            if not content_elem:
                content_elem = result.find('div', class_='c-abstract')
            if content_elem:
                item['content'] = content_elem.get_text(strip=True)
            else:
                item['content'] = ''
            
            # 设置其他字段
            item['source'] = '百度'
            item['keyword'] = self.keyword
            item['crawl_time'] = self.crawl_time
            item['publish_time'] = ''  # 百度搜索结果通常不显示发布时间
            
            yield item
    
    def _parse_bing(self, response):
        """解析Bing搜索结果"""
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Bing搜索结果的选择器
        results = soup.find_all('li', class_='b_algo')
        
        for result in results:
            item = SearchResultItem()
            
            # 提取标题和链接
            title_elem = result.find('h2')
            if title_elem:
                title_link = title_elem.find('a')
                if title_link:
                    item['title'] = title_link.get_text(strip=True)
                    item['url'] = title_link.get('href', '')
                else:
                    continue
            else:
                continue
            
            # 提取内容摘要
            content_elem = result.find('p')
            if content_elem:
                item['content'] = content_elem.get_text(strip=True)
            else:
                item['content'] = ''
            
            # 设置其他字段
            item['source'] = 'Bing'
            item['keyword'] = self.keyword
            item['crawl_time'] = self.crawl_time
            item['publish_time'] = ''
            
            yield item
    
    def _parse_google(self, response):
        """解析Google搜索结果"""
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Google搜索结果的选择器
        results = soup.find_all('div', class_='g')
        
        for result in results:
            item = SearchResultItem()
            
            # 提取标题和链接
            title_elem = result.find('h3')
            if title_elem:
                title_link = title_elem.find_parent('a')
                if title_link:
                    item['title'] = title_elem.get_text(strip=True)
                    item['url'] = title_link.get('href', '')
                else:
                    continue
            else:
                continue
            
            # 提取内容摘要
            content_elem = result.find('span', {'data-ved': True})
            if not content_elem:
                content_elem = result.find('div', class_='VwiC3b')
            if content_elem:
                item['content'] = content_elem.get_text(strip=True)
            else:
                item['content'] = ''
            
            # 设置其他字段
            item['source'] = 'Google'
            item['keyword'] = self.keyword
            item['crawl_time'] = self.crawl_time
            item['publish_time'] = ''
            
            yield item
