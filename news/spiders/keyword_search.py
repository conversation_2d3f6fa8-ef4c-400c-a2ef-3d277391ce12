import scrapy
import requests
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import quote, urljoin
from scrapy.http import HtmlResponse
from news.items import SearchResultItem
from news.cache_manager import cache_manager
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time


class KeywordSearchSpider(scrapy.Spider):
    name = 'keyword_search'
    allowed_domains = ['movie.douban.com']

    def __init__(self, keyword=None, source='douban', *args, **kwargs):
        super(KeywordSearchSpider, self).__init__(*args, **kwargs)
        self.keyword = keyword
        self.source = 'douban'  # 固定使用豆瓣
        self.crawl_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.driver = None

        if not self.keyword:
            raise ValueError("必须提供搜索关键字")

        # 初始化Selenium WebDriver
        self._setup_driver()

    def _setup_driver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36')

            # 自动下载并设置ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)

        except Exception as e:
            self.logger.error(f"设置WebDriver失败: {e}")
            self.driver = None

    def start_requests(self):
        """生成一个虚拟请求来触发爬虫"""
        # 生成一个虚拟请求，实际的搜索在parse方法中进行
        yield scrapy.Request(
            url='https://movie.douban.com/',
            callback=self.parse,
            dont_filter=True
        )

    def _search_with_selenium(self):
        """使用Selenium进行豆瓣电影搜索"""
        try:
            # 访问豆瓣电影首页
            self.driver.get('https://movie.douban.com/')
            time.sleep(2)

            # 查找搜索框并输入关键字
            search_box = self.driver.find_element(By.NAME, 'search_text')
            search_box.clear()
            search_box.send_keys(self.keyword)

            # 点击搜索按钮
            search_button = self.driver.find_element(By.XPATH, '//input[@type="submit" and @value="搜索"]')
            search_button.click()

            # 等待搜索结果加载
            time.sleep(3)

            # 获取页面源码并解析
            page_source = self.driver.page_source
            return self._parse_search_results(page_source)

        except Exception as e:
            self.logger.error(f"Selenium搜索失败: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def parse(self, response):
        """使用Selenium进行搜索并返回结果"""
        if self.driver:
            items = self._search_with_selenium()
            results_list = []

            for item in items:
                # 收集结果用于缓存
                result_dict = {
                    'title': item.get('title', ''),
                    'url': item.get('url', ''),
                    'content': item.get('content', ''),
                    'source': item.get('source', ''),
                    'keyword': item.get('keyword', ''),
                    'crawl_time': item.get('crawl_time', ''),
                    'publish_time': item.get('publish_time', '')
                }
                results_list.append(result_dict)
                yield item

            # 直接将结果写入缓存，以关键字为key
            if results_list:
                cache_success = cache_manager.set_search_results(self.keyword, results_list, self.source)
                if cache_success:
                    self.logger.info(f"搜索结果已直接缓存: {self.keyword} ({len(results_list)} 条)")
                else:
                    self.logger.warning(f"缓存写入失败: {self.keyword}")
            else:
                self.logger.warning(f"没有找到搜索结果: {self.keyword}")

    def _parse_search_results(self, page_source):
        """解析搜索结果页面源码"""
        # 使用BeautifulSoup解析
        soup = BeautifulSoup(page_source, 'html.parser')

        # 尝试多种可能的选择器
        results = []

        # 尝试搜索结果的不同选择器
        selectors = [
            'div.item-root',
            'div.result',
            'div.result-item',
            'div.search-result',
            'div.movie-item',
            'li.result-item'
        ]

        for selector in selectors:
            results = soup.select(selector)
            if results:
                self.logger.info(f"找到 {len(results)} 个结果，使用选择器: {selector}")
                break

        if not results:
            # 如果没有找到结果，尝试查找任何包含电影信息的元素
            results = soup.find_all('div', class_=lambda x: x and ('item' in x or 'result' in x or 'movie' in x))
            self.logger.info(f"使用通用选择器找到 {len(results)} 个结果")

        items = []
        for result in results:
            item = self._extract_movie_info(result, soup)
            if item:
                items.append(item)

        return items

    def _extract_movie_info(self, result, soup):
        """从结果元素中提取电影信息"""
        try:
            item = SearchResultItem()

            # 尝试提取标题
            title = self._extract_title(result)
            if not title:
                return None

            item['title'] = title
            item['url'] = self._extract_url(result)
            item['content'] = self._extract_content(result)
            item['source'] = '豆瓣电影'
            item['keyword'] = self.keyword
            item['crawl_time'] = self.crawl_time
            item['publish_time'] = ''

            return item

        except Exception as e:
            self.logger.error(f"提取电影信息失败: {e}")
            return None

    def _extract_title(self, result):
        """提取电影标题"""
        # 尝试多种标题选择器
        title_selectors = [
            'a[title]',
            'h3 a',
            'h2 a',
            '.title',
            '.movie-title',
            '.subject-title'
        ]

        for selector in title_selectors:
            elem = result.select_one(selector)
            if elem:
                title = elem.get('title') or elem.get_text(strip=True)
                if title:
                    return title

        return None

    def _extract_url(self, result):
        """提取电影链接"""
        link_elem = result.find('a', href=True)
        if link_elem:
            href = link_elem.get('href', '')
            if href.startswith('/'):
                return f"https://movie.douban.com{href}"
            elif href.startswith('http'):
                return href
        return ''

    def _extract_content(self, result):
        """提取电影描述信息"""
        content_parts = []

        # 尝试提取评分
        rating_elem = result.find('span', class_=lambda x: x and 'rating' in x)
        if rating_elem:
            rating = rating_elem.get_text(strip=True)
            if rating:
                content_parts.append(f"评分: {rating}")

        # 尝试提取年份、导演等信息
        meta_elem = result.find('div', class_=lambda x: x and ('meta' in x or 'info' in x))
        if meta_elem:
            meta_text = meta_elem.get_text(strip=True)
            if meta_text:
                content_parts.append(meta_text)

        return ' | '.join(content_parts) if content_parts else ''


