# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class NewsItem(scrapy.Item):
    # define the fields for your item here like:
    # name = scrapy.Field()
    title = scrapy.Field()
    score = scrapy.Field()
    motto = scrapy.Field()
    pass


class SearchResultItem(scrapy.Item):
    """通用搜索结果数据结构"""
    title = scrapy.Field()          # 标题
    url = scrapy.Field()            # 链接
    content = scrapy.Field()        # 内容摘要
    source = scrapy.Field()         # 来源网站
    publish_time = scrapy.Field()   # 发布时间
    keyword = scrapy.Field()        # 搜索关键字
    crawl_time = scrapy.Field()     # 爬取时间
