#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin, urlparse
from news.cache_manager import cache_manager
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)


class SourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.timeout = 10

    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """提取网页主要内容"""
        # 移除脚本和样式标签
        for script in soup(["script", "style", "nav", "header", "footer", "aside"]):
            script.decompose()

        # 尝试找到主要内容区域
        content_selectors = [
            'article', 'main', '.content', '.post', '.entry',
            '.article-content', '.post-content', '.entry-content',
            '#content', '#main', '#article'
        ]

        main_content = None
        for selector in content_selectors:
            main_content = soup.select_one(selector)
            if main_content:
                break

        # 如果没找到主要内容区域，使用body
        if not main_content:
            main_content = soup.find('body')

        if not main_content:
            main_content = soup

        # 提取文本
        text = main_content.get_text(separator='\n', strip=True)

        # 清理文本
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 10:  # 过滤太短的行
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)
    
    def add_source_rule(self, key: str, url: str, ttl: int = 7200) -> Dict[str, Any]:
        """
        添加数据源规则

        Args:
            key: 规则键名
            url: 数据源URL
            ttl: 缓存时间（秒）

        Returns:
            操作结果
        """
        try:
            # 使用requests + BeautifulSoup解析网页
            response = requests.get(url, headers=self.headers, timeout=self.timeout)
            response.raise_for_status()
            response.encoding = response.apparent_encoding

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取标题
            title = ''
            title_tags = soup.find_all(['title', 'h1', 'h2'])
            if title_tags:
                title = title_tags[0].get_text(strip=True)

            # 提取正文内容
            text = self._extract_main_content(soup)

            # 提取摘要（取前200字符）
            summary = text[:200] + '...' if len(text) > 200 else text

            # 提取图片
            images = []
            img_tags = soup.find_all('img', src=True)
            for img in img_tags[:10]:  # 最多10张图片
                img_url = urljoin(url, img['src'])
                images.append(img_url)

            # 提取meta信息
            meta_description = ''
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                meta_description = meta_desc.get('content', '')

            meta_keywords = ''
            meta_kw = soup.find('meta', attrs={'name': 'keywords'})
            if meta_kw:
                meta_keywords = meta_kw.get('content', '')

            # 提取数据
            extracted_data = {
                'key': key,
                'url': url,
                'title': title,
                'text': text,
                'summary': summary,
                'authors': [],  # 简化版本不提取作者
                'publish_date': None,  # 简化版本不提取发布日期
                'top_image': images[0] if images else '',
                'images': images,
                'keywords': meta_keywords.split(',') if meta_keywords else [],
                'meta_keywords': meta_keywords,
                'meta_description': meta_description,
                'canonical_link': url,
                'extracted_at': datetime.now().isoformat(),
                'ttl': ttl
            }
            
            # 存储到缓存
            cache_key = f"source_rule:{key}"
            success = cache_manager.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(extracted_data, ensure_ascii=False)
            ) if cache_manager.redis_client else False
            
            if success:
                logger.info(f"数据源规则已缓存: {key} -> {url}")
                return {
                    'success': True,
                    'message': f'数据源规则 "{key}" 添加成功',
                    'data': extracted_data
                }
            else:
                return {
                    'success': False,
                    'message': '缓存写入失败',
                    'data': extracted_data
                }
                
        except Exception as e:
            logger.error(f"添加数据源规则失败: {e}")
            return {
                'success': False,
                'message': f'解析网页失败: {str(e)}',
                'error': str(e)
            }
    
    def get_source_rule(self, key: str) -> Optional[Dict[str, Any]]:
        """
        获取数据源规则
        
        Args:
            key: 规则键名
            
        Returns:
            规则数据或None
        """
        if not cache_manager.redis_client:
            return None
        
        try:
            cache_key = f"source_rule:{key}"
            cached_data = cache_manager.redis_client.get(cache_key)
            
            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"从缓存获取数据源规则: {key}")
                return data
                
        except Exception as e:
            logger.error(f"获取数据源规则失败: {e}")
        
        return None
    
    def list_source_rules(self) -> List[Dict[str, Any]]:
        """
        获取所有数据源规则列表
        
        Returns:
            规则列表
        """
        if not cache_manager.redis_client:
            return []
        
        try:
            # 查找所有数据源规则键
            rule_keys = cache_manager.redis_client.keys("source_rule:*")
            rules = []
            
            for key in rule_keys:
                try:
                    data = cache_manager.redis_client.get(key)
                    if data:
                        rule_info = json.loads(data)
                        # 获取TTL
                        ttl = cache_manager.redis_client.ttl(key)
                        rule_info['remaining_ttl'] = ttl if ttl > 0 else 0
                        rules.append(rule_info)
                except Exception as e:
                    logger.warning(f"解析规则键 {key} 失败: {e}")
                    continue
            
            # 按提取时间排序
            rules.sort(key=lambda x: x.get('extracted_at', ''), reverse=True)
            return rules
            
        except Exception as e:
            logger.error(f"获取数据源规则列表失败: {e}")
            return []
    
    def delete_source_rule(self, key: str) -> bool:
        """
        删除数据源规则
        
        Args:
            key: 规则键名
            
        Returns:
            是否删除成功
        """
        if not cache_manager.redis_client:
            return False
        
        try:
            cache_key = f"source_rule:{key}"
            result = cache_manager.redis_client.delete(cache_key)
            
            if result:
                logger.info(f"删除数据源规则: {key}")
                return True
                
        except Exception as e:
            logger.error(f"删除数据源规则失败: {e}")
        
        return False
    
    def search_by_keyword_and_rule(self, keyword: str, rule_key: str) -> Dict[str, Any]:
        """
        根据关键字和规则进行搜索
        
        Args:
            keyword: 搜索关键字
            rule_key: 规则键名
            
        Returns:
            搜索结果
        """
        try:
            # 获取规则
            rule_data = self.get_source_rule(rule_key)
            if not rule_data:
                return {
                    'success': False,
                    'message': f'规则 "{rule_key}" 不存在'
                }
            
            # 在规则数据中搜索关键字
            search_results = []
            text_content = rule_data.get('text', '')
            title = rule_data.get('title', '')
            summary = rule_data.get('summary', '')
            
            # 简单的关键字匹配
            if (keyword.lower() in text_content.lower() or 
                keyword.lower() in title.lower() or 
                keyword.lower() in summary.lower()):
                
                # 提取包含关键字的段落
                paragraphs = text_content.split('\n')
                matching_paragraphs = [
                    p.strip() for p in paragraphs 
                    if keyword.lower() in p.lower() and len(p.strip()) > 10
                ]
                
                search_result = {
                    'title': title,
                    'url': rule_data.get('url'),
                    'content': ' '.join(matching_paragraphs[:3]),  # 前3个匹配段落
                    'source': rule_key,
                    'keyword': keyword,
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'publish_time': rule_data.get('publish_date', ''),
                    'summary': summary,
                    'authors': rule_data.get('authors', []),
                    'top_image': rule_data.get('top_image', ''),
                    'match_count': len(matching_paragraphs)
                }
                search_results.append(search_result)
            
            # 缓存搜索结果
            if search_results:
                cache_success = cache_manager.set_search_results(
                    f"{keyword}_{rule_key}", 
                    search_results, 
                    rule_key
                )
                
                return {
                    'success': True,
                    'message': f'在规则 "{rule_key}" 中找到 {len(search_results)} 个结果',
                    'results': search_results,
                    'cached': cache_success
                }
            else:
                return {
                    'success': True,
                    'message': f'在规则 "{rule_key}" 中未找到关键字 "{keyword}"',
                    'results': [],
                    'cached': False
                }
                
        except Exception as e:
            logger.error(f"关键字规则搜索失败: {e}")
            return {
                'success': False,
                'message': f'搜索失败: {str(e)}',
                'error': str(e)
            }


# 全局数据源管理器实例
source_manager = SourceManager()
