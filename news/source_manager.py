#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from newspaper import Article, Config
from news.cache_manager import cache_manager

logger = logging.getLogger(__name__)


class SourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.config = Config()
        self.config.browser_user_agent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36'
        self.config.request_timeout = 10
        self.config.number_threads = 1
    
    def add_source_rule(self, key: str, url: str, ttl: int = 7200) -> Dict[str, Any]:
        """
        添加数据源规则
        
        Args:
            key: 规则键名
            url: 数据源URL
            ttl: 缓存时间（秒）
            
        Returns:
            操作结果
        """
        try:
            # 使用newspaper3k解析网页
            article = Article(url, config=self.config)
            article.download()
            article.parse()
            
            # 提取数据
            extracted_data = {
                'key': key,
                'url': url,
                'title': article.title,
                'text': article.text,
                'summary': article.summary if hasattr(article, 'summary') else '',
                'authors': article.authors,
                'publish_date': article.publish_date.isoformat() if article.publish_date else None,
                'top_image': article.top_image,
                'images': list(article.images),
                'keywords': article.keywords if hasattr(article, 'keywords') else [],
                'meta_keywords': article.meta_keywords,
                'meta_description': article.meta_description,
                'canonical_link': article.canonical_link,
                'extracted_at': datetime.now().isoformat(),
                'ttl': ttl
            }
            
            # 存储到缓存
            cache_key = f"source_rule:{key}"
            success = cache_manager.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(extracted_data, ensure_ascii=False)
            ) if cache_manager.redis_client else False
            
            if success:
                logger.info(f"数据源规则已缓存: {key} -> {url}")
                return {
                    'success': True,
                    'message': f'数据源规则 "{key}" 添加成功',
                    'data': extracted_data
                }
            else:
                return {
                    'success': False,
                    'message': '缓存写入失败',
                    'data': extracted_data
                }
                
        except Exception as e:
            logger.error(f"添加数据源规则失败: {e}")
            return {
                'success': False,
                'message': f'解析网页失败: {str(e)}',
                'error': str(e)
            }
    
    def get_source_rule(self, key: str) -> Optional[Dict[str, Any]]:
        """
        获取数据源规则
        
        Args:
            key: 规则键名
            
        Returns:
            规则数据或None
        """
        if not cache_manager.redis_client:
            return None
        
        try:
            cache_key = f"source_rule:{key}"
            cached_data = cache_manager.redis_client.get(cache_key)
            
            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"从缓存获取数据源规则: {key}")
                return data
                
        except Exception as e:
            logger.error(f"获取数据源规则失败: {e}")
        
        return None
    
    def list_source_rules(self) -> List[Dict[str, Any]]:
        """
        获取所有数据源规则列表
        
        Returns:
            规则列表
        """
        if not cache_manager.redis_client:
            return []
        
        try:
            # 查找所有数据源规则键
            rule_keys = cache_manager.redis_client.keys("source_rule:*")
            rules = []
            
            for key in rule_keys:
                try:
                    data = cache_manager.redis_client.get(key)
                    if data:
                        rule_info = json.loads(data)
                        # 获取TTL
                        ttl = cache_manager.redis_client.ttl(key)
                        rule_info['remaining_ttl'] = ttl if ttl > 0 else 0
                        rules.append(rule_info)
                except Exception as e:
                    logger.warning(f"解析规则键 {key} 失败: {e}")
                    continue
            
            # 按提取时间排序
            rules.sort(key=lambda x: x.get('extracted_at', ''), reverse=True)
            return rules
            
        except Exception as e:
            logger.error(f"获取数据源规则列表失败: {e}")
            return []
    
    def delete_source_rule(self, key: str) -> bool:
        """
        删除数据源规则
        
        Args:
            key: 规则键名
            
        Returns:
            是否删除成功
        """
        if not cache_manager.redis_client:
            return False
        
        try:
            cache_key = f"source_rule:{key}"
            result = cache_manager.redis_client.delete(cache_key)
            
            if result:
                logger.info(f"删除数据源规则: {key}")
                return True
                
        except Exception as e:
            logger.error(f"删除数据源规则失败: {e}")
        
        return False
    
    def search_by_keyword_and_rule(self, keyword: str, rule_key: str) -> Dict[str, Any]:
        """
        根据关键字和规则进行搜索
        
        Args:
            keyword: 搜索关键字
            rule_key: 规则键名
            
        Returns:
            搜索结果
        """
        try:
            # 获取规则
            rule_data = self.get_source_rule(rule_key)
            if not rule_data:
                return {
                    'success': False,
                    'message': f'规则 "{rule_key}" 不存在'
                }
            
            # 在规则数据中搜索关键字
            search_results = []
            text_content = rule_data.get('text', '')
            title = rule_data.get('title', '')
            summary = rule_data.get('summary', '')
            
            # 简单的关键字匹配
            if (keyword.lower() in text_content.lower() or 
                keyword.lower() in title.lower() or 
                keyword.lower() in summary.lower()):
                
                # 提取包含关键字的段落
                paragraphs = text_content.split('\n')
                matching_paragraphs = [
                    p.strip() for p in paragraphs 
                    if keyword.lower() in p.lower() and len(p.strip()) > 10
                ]
                
                search_result = {
                    'title': title,
                    'url': rule_data.get('url'),
                    'content': ' '.join(matching_paragraphs[:3]),  # 前3个匹配段落
                    'source': rule_key,
                    'keyword': keyword,
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'publish_time': rule_data.get('publish_date', ''),
                    'summary': summary,
                    'authors': rule_data.get('authors', []),
                    'top_image': rule_data.get('top_image', ''),
                    'match_count': len(matching_paragraphs)
                }
                search_results.append(search_result)
            
            # 缓存搜索结果
            if search_results:
                cache_success = cache_manager.set_search_results(
                    f"{keyword}_{rule_key}", 
                    search_results, 
                    rule_key
                )
                
                return {
                    'success': True,
                    'message': f'在规则 "{rule_key}" 中找到 {len(search_results)} 个结果',
                    'results': search_results,
                    'cached': cache_success
                }
            else:
                return {
                    'success': True,
                    'message': f'在规则 "{rule_key}" 中未找到关键字 "{keyword}"',
                    'results': [],
                    'cached': False
                }
                
        except Exception as e:
            logger.error(f"关键字规则搜索失败: {e}")
            return {
                'success': False,
                'message': f'搜索失败: {str(e)}',
                'error': str(e)
            }


# 全局数据源管理器实例
source_manager = SourceManager()
