#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin, urlparse
from news.cache_manager import cache_manager
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)


class SourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.timeout = 10
        self.driver = None

    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """提取网页主要内容"""
        # 移除脚本和样式标签
        for script in soup(["script", "style", "nav", "header", "footer", "aside"]):
            script.decompose()

        # 尝试找到主要内容区域
        content_selectors = [
            'article', 'main', '.content', '.post', '.entry',
            '.article-content', '.post-content', '.entry-content',
            '#content', '#main', '#article'
        ]

        main_content = None
        for selector in content_selectors:
            main_content = soup.select_one(selector)
            if main_content:
                break

        # 如果没找到主要内容区域，使用body
        if not main_content:
            main_content = soup.find('body')

        if not main_content:
            main_content = soup

        # 提取文本
        text = main_content.get_text(separator='\n', strip=True)

        # 清理文本
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 10:  # 过滤太短的行
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _setup_selenium_driver(self):
        """设置Selenium WebDriver"""
        if self.driver:
            return True

        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36')

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)

            logger.info("Selenium WebDriver 初始化成功")
            return True

        except Exception as e:
            logger.error(f"Selenium WebDriver 初始化失败: {e}")
            self.driver = None
            return False

    def _close_selenium_driver(self):
        """关闭Selenium WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                logger.info("Selenium WebDriver 已关闭")
            except Exception as e:
                logger.error(f"关闭Selenium WebDriver失败: {e}")

    def _extract_with_selenium(self, url: str, auth_token: str = None, auth_type: str = 'Bearer') -> tuple:
        """使用Selenium提取网页内容"""
        if not self._setup_selenium_driver():
            return None, []

        try:
            logger.info(f"Selenium访问: {url}")

            # 如果有认证token，先设置请求头
            if auth_token:
                logger.info(f"设置认证头: {auth_type}")
                # 通过CDP (Chrome DevTools Protocol) 设置请求头
                if auth_type == 'Bearer':
                    auth_header = f'Bearer {auth_token}'
                elif auth_type == 'Token':
                    auth_header = f'Token {auth_token}'
                elif auth_type == 'Custom':
                    auth_header = auth_token
                else:
                    auth_header = f'Bearer {auth_token}'

                # 设置请求拦截器
                self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                    "userAgent": self.headers['User-Agent']
                })

                # 启用网络域
                self.driver.execute_cdp_cmd('Network.enable', {})

                # 设置额外的请求头
                if auth_type == 'API-Key':
                    self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                        "userAgent": self.headers['User-Agent'],
                        "platform": "Web",
                        "acceptLanguage": "en-US,en;q=0.9"
                    })
                    # 对于API-Key，我们需要通过JavaScript注入的方式
                    self.driver.execute_script(f"""
                        // 拦截fetch请求并添加API-Key头
                        const originalFetch = window.fetch;
                        window.fetch = function(...args) {{
                            if (args[1]) {{
                                args[1].headers = args[1].headers || {{}};
                                args[1].headers['X-API-Key'] = '{auth_token}';
                            }} else {{
                                args[1] = {{
                                    headers: {{'X-API-Key': '{auth_token}'}}
                                }};
                            }}
                            return originalFetch.apply(this, args);
                        }};
                    """)
                else:
                    # 对于Authorization头
                    self.driver.execute_script(f"""
                        // 拦截fetch请求并添加Authorization头
                        const originalFetch = window.fetch;
                        window.fetch = function(...args) {{
                            if (args[1]) {{
                                args[1].headers = args[1].headers || {{}};
                                args[1].headers['Authorization'] = '{auth_header}';
                            }} else {{
                                args[1] = {{
                                    headers: {{'Authorization': '{auth_header}'}}
                                }};
                            }}
                            return originalFetch.apply(this, args);
                        }};
                    """)

            self.driver.get(url)

            # 等待页面基本加载
            import time
            time.sleep(3)

            # 检查是否被重定向到登录页面
            current_url = self.driver.current_url
            if 'login' in current_url.lower() or 'signin' in current_url.lower():
                logger.warning(f"页面被重定向到登录页面: {current_url}")
                # 尝试直接访问原URL
                self.driver.get(url)
                time.sleep(3)

            # 尝试滚动页面触发懒加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            # 等待动态内容加载
            try:
                # 等待图片或内容容器
                WebDriverWait(self.driver, 10).until(
                    lambda driver: len(driver.find_elements(By.TAG_NAME, "img")) > 0 or
                                  len(driver.find_elements(By.CSS_SELECTOR, "[style*='background-image']")) > 0 or
                                  len(driver.find_elements(By.CSS_SELECTOR, ".image, .photo, .picture, .gallery")) > 0
                )
                logger.info("检测到图片或图片容器")
            except:
                logger.warning("未检测到图片元素，继续处理")

            # 再次滚动确保所有内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # 提取文本内容
            text = self._extract_main_content(soup)

            # 提取图片 - 针对动态加载的图片
            images = self._extract_dynamic_images(soup, url)

            logger.info(f"Selenium提取完成: 文本长度={len(text)}, 图片数量={len(images)}")
            return text, images

        except Exception as e:
            logger.error(f"Selenium提取失败: {e}")
            return None, []

    def _extract_dynamic_images(self, soup: BeautifulSoup, base_url: str) -> list:
        """提取动态加载的图片"""
        images = []

        # 更全面的图片选择器
        img_selectors = [
            'img[src]',
            'img[data-src]',
            'img[data-lazy]',
            'img[data-original]',
            'img[data-srcset]',
            '[style*="background-image"]',
            '.image img',
            '.gallery img',
            '.photo img',
            '.picture img',
            '.thumbnail img',
            '.preview img',
            'figure img',
            'picture img',
            # 针对特定网站的选择器
            '[class*="image"] img',
            '[class*="photo"] img',
            '[class*="picture"] img',
            '[id*="image"] img',
            '[data-testid*="image"] img'
        ]

        for selector in img_selectors:
            elements = soup.select(selector)
            for element in elements:
                img_url = None

                # 尝试不同的属性
                for attr in ['src', 'data-src', 'data-lazy', 'data-original', 'data-srcset', 'data-url']:
                    if element.get(attr):
                        img_url = element.get(attr)
                        # 处理srcset（取第一个URL）
                        if 'srcset' in attr and ',' in img_url:
                            img_url = img_url.split(',')[0].strip().split(' ')[0]
                        break

                # 处理背景图片
                if not img_url and 'style' in element.attrs:
                    style = element.get('style', '')
                    import re
                    bg_match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
                    if bg_match:
                        img_url = bg_match.group(1)

                if img_url:
                    # 清理URL
                    img_url = img_url.strip()

                    # 转换为绝对URL
                    if img_url.startswith('//'):
                        img_url = 'https:' + img_url
                    elif img_url.startswith('/'):
                        img_url = urljoin(base_url, img_url)
                    elif not img_url.startswith(('http://', 'https://')):
                        img_url = urljoin(base_url, img_url)

                    # 过滤掉明显不是图片的URL和无效URL
                    if (img_url.startswith(('http://', 'https://')) and
                        (any(ext in img_url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']) or
                         'image' in img_url.lower() or 'photo' in img_url.lower() or 'picture' in img_url.lower())):

                        # 避免重复
                        if img_url not in images:
                            images.append(img_url)

        # 如果没有找到图片，尝试查找可能包含图片URL的JavaScript变量或JSON
        if not images:
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    import re
                    # 查找可能的图片URL
                    urls = re.findall(r'https?://[^\s"\'<>]+\.(?:jpg|jpeg|png|gif|webp|svg)', script.string, re.IGNORECASE)
                    for url in urls[:10]:  # 最多10个
                        if url not in images:
                            images.append(url)

        logger.info(f"提取到 {len(images)} 张图片")
        return images[:30]  # 最多返回30张图片
    
    def add_source_rule(self, key: str, url: str, ttl: int = 7200, use_selenium: bool = None, auth_token: str = None, auth_type: str = 'Bearer') -> Dict[str, Any]:
        """
        添加数据源规则

        Args:
            key: 规则键名
            url: 数据源URL
            ttl: 缓存时间（秒）
            use_selenium: 是否使用Selenium渲染，None为自动检测
            auth_token: 认证token
            auth_type: 认证类型 ('Bearer', 'Token', 'API-Key', 'Custom')

        Returns:
            操作结果
        """
        try:
            # 自动检测是否需要使用Selenium
            if use_selenium is None:
                # 对于已知的JavaScript重度网站，自动使用Selenium
                js_heavy_domains = [
                    'piclumen.com',
                    'midjourney.com',
                    'openai.com',
                    'huggingface.co'
                ]
                use_selenium = any(domain in url.lower() for domain in js_heavy_domains)

            text = ''
            images = []
            title = ''
            meta_description = ''
            meta_keywords = ''

            if use_selenium:
                logger.info(f"使用Selenium渲染: {url}")
                # 使用Selenium提取内容
                text, images = self._extract_with_selenium(url, auth_token, auth_type)

                if text is None:
                    # Selenium失败，回退到requests
                    logger.warning("Selenium提取失败，回退到requests方法")
                    use_selenium = False
                else:
                    # 使用Selenium获取的页面源码提取其他信息
                    if self.driver:
                        page_source = self.driver.page_source
                        soup = BeautifulSoup(page_source, 'html.parser')

                        # 提取标题
                        title_tags = soup.find_all(['title', 'h1', 'h2'])
                        if title_tags:
                            title = title_tags[0].get_text(strip=True)

                        # 提取meta信息
                        meta_desc = soup.find('meta', attrs={'name': 'description'})
                        if meta_desc:
                            meta_description = meta_desc.get('content', '')

                        meta_kw = soup.find('meta', attrs={'name': 'keywords'})
                        if meta_kw:
                            meta_keywords = meta_kw.get('content', '')

                    # 关闭Selenium driver
                    self._close_selenium_driver()

            if not use_selenium:
                logger.info(f"使用requests + BeautifulSoup: {url}")
                # 准备请求头
                request_headers = self.headers.copy()
                if auth_token:
                    if auth_type == 'Bearer':
                        request_headers['Authorization'] = f'Bearer {auth_token}'
                    elif auth_type == 'Token':
                        request_headers['Authorization'] = f'Token {auth_token}'
                    elif auth_type == 'API-Key':
                        request_headers['X-API-Key'] = auth_token
                    elif auth_type == 'Custom':
                        request_headers['Authorization'] = auth_token
                    logger.info(f"添加认证头: {auth_type}")

                # 使用传统方法
                response = requests.get(url, headers=request_headers, timeout=self.timeout)
                response.raise_for_status()
                response.encoding = response.apparent_encoding

                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # 提取标题
                title_tags = soup.find_all(['title', 'h1', 'h2'])
                if title_tags:
                    title = title_tags[0].get_text(strip=True)

                # 提取正文内容
                text = self._extract_main_content(soup)

                # 提取图片
                img_tags = soup.find_all('img', src=True)
                for img in img_tags[:10]:  # 最多10张图片
                    img_url = urljoin(url, img['src'])
                    images.append(img_url)

                # 提取meta信息
                meta_desc = soup.find('meta', attrs={'name': 'description'})
                if meta_desc:
                    meta_description = meta_desc.get('content', '')

                meta_kw = soup.find('meta', attrs={'name': 'keywords'})
                if meta_kw:
                    meta_keywords = meta_kw.get('content', '')

            # 提取摘要（取前200字符）
            summary = text[:200] + '...' if len(text) > 200 else text

            # 提取数据
            extracted_data = {
                'key': key,
                'url': url,
                'title': title,
                'text': text,
                'summary': summary,
                'authors': [],  # 简化版本不提取作者
                'publish_date': None,  # 简化版本不提取发布日期
                'top_image': images[0] if images else '',
                'images': images,
                'keywords': meta_keywords.split(',') if meta_keywords else [],
                'meta_keywords': meta_keywords,
                'meta_description': meta_description,
                'canonical_link': url,
                'extracted_at': datetime.now().isoformat(),
                'ttl': ttl,
                'use_selenium': use_selenium,
                'images_count': len(images),
                'auth_type': auth_type if auth_token else None,
                'has_auth': bool(auth_token)
            }
            
            # 存储到缓存
            cache_key = f"source_rule:{key}"
            success = cache_manager.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(extracted_data, ensure_ascii=False)
            ) if cache_manager.redis_client else False
            
            if success:
                logger.info(f"数据源规则已缓存: {key} -> {url}")
                return {
                    'success': True,
                    'message': f'数据源规则 "{key}" 添加成功',
                    'data': extracted_data
                }
            else:
                return {
                    'success': False,
                    'message': '缓存写入失败',
                    'data': extracted_data
                }
                
        except Exception as e:
            logger.error(f"添加数据源规则失败: {e}")
            return {
                'success': False,
                'message': f'解析网页失败: {str(e)}',
                'error': str(e)
            }
    
    def get_source_rule(self, key: str) -> Optional[Dict[str, Any]]:
        """
        获取数据源规则
        
        Args:
            key: 规则键名
            
        Returns:
            规则数据或None
        """
        if not cache_manager.redis_client:
            return None
        
        try:
            cache_key = f"source_rule:{key}"
            cached_data = cache_manager.redis_client.get(cache_key)
            
            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"从缓存获取数据源规则: {key}")
                return data
                
        except Exception as e:
            logger.error(f"获取数据源规则失败: {e}")
        
        return None
    
    def list_source_rules(self) -> List[Dict[str, Any]]:
        """
        获取所有数据源规则列表
        
        Returns:
            规则列表
        """
        if not cache_manager.redis_client:
            return []
        
        try:
            # 查找所有数据源规则键
            rule_keys = cache_manager.redis_client.keys("source_rule:*")
            rules = []
            
            for key in rule_keys:
                try:
                    data = cache_manager.redis_client.get(key)
                    if data:
                        rule_info = json.loads(data)
                        # 获取TTL
                        ttl = cache_manager.redis_client.ttl(key)
                        rule_info['remaining_ttl'] = ttl if ttl > 0 else 0
                        rules.append(rule_info)
                except Exception as e:
                    logger.warning(f"解析规则键 {key} 失败: {e}")
                    continue
            
            # 按提取时间排序
            rules.sort(key=lambda x: x.get('extracted_at', ''), reverse=True)
            return rules
            
        except Exception as e:
            logger.error(f"获取数据源规则列表失败: {e}")
            return []
    
    def delete_source_rule(self, key: str) -> bool:
        """
        删除数据源规则
        
        Args:
            key: 规则键名
            
        Returns:
            是否删除成功
        """
        if not cache_manager.redis_client:
            return False
        
        try:
            cache_key = f"source_rule:{key}"
            result = cache_manager.redis_client.delete(cache_key)
            
            if result:
                logger.info(f"删除数据源规则: {key}")
                return True
                
        except Exception as e:
            logger.error(f"删除数据源规则失败: {e}")
        
        return False
    
    def search_by_keyword_and_rule(self, keyword: str, rule_key: str) -> Dict[str, Any]:
        """
        根据关键字和规则进行搜索
        
        Args:
            keyword: 搜索关键字
            rule_key: 规则键名
            
        Returns:
            搜索结果
        """
        try:
            # 获取规则
            rule_data = self.get_source_rule(rule_key)
            if not rule_data:
                return {
                    'success': False,
                    'message': f'规则 "{rule_key}" 不存在'
                }
            
            # 在规则数据中搜索关键字
            search_results = []
            text_content = rule_data.get('text', '')
            title = rule_data.get('title', '')
            summary = rule_data.get('summary', '')
            
            # 简单的关键字匹配
            if (keyword.lower() in text_content.lower() or 
                keyword.lower() in title.lower() or 
                keyword.lower() in summary.lower()):
                
                # 提取包含关键字的段落
                paragraphs = text_content.split('\n')
                matching_paragraphs = [
                    p.strip() for p in paragraphs 
                    if keyword.lower() in p.lower() and len(p.strip()) > 10
                ]
                
                search_result = {
                    'title': title,
                    'url': rule_data.get('url'),
                    'content': ' '.join(matching_paragraphs[:3]),  # 前3个匹配段落
                    'source': rule_key,
                    'keyword': keyword,
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'publish_time': rule_data.get('publish_date', ''),
                    'summary': summary,
                    'authors': rule_data.get('authors', []),
                    'top_image': rule_data.get('top_image', ''),
                    'match_count': len(matching_paragraphs)
                }
                search_results.append(search_result)
            
            # 缓存搜索结果
            if search_results:
                cache_success = cache_manager.set_search_results(
                    f"{keyword}_{rule_key}", 
                    search_results, 
                    rule_key
                )
                
                return {
                    'success': True,
                    'message': f'在规则 "{rule_key}" 中找到 {len(search_results)} 个结果',
                    'results': search_results,
                    'cached': cache_success
                }
            else:
                return {
                    'success': True,
                    'message': f'在规则 "{rule_key}" 中未找到关键字 "{keyword}"',
                    'results': [],
                    'cached': False
                }
                
        except Exception as e:
            logger.error(f"关键字规则搜索失败: {e}")
            return {
                'success': False,
                'message': f'搜索失败: {str(e)}',
                'error': str(e)
            }


# 全局数据源管理器实例
source_manager = SourceManager()
