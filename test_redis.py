#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append('.')

from news.cache_manager import cache_manager

def test_redis_connection():
    """测试Redis连接"""
    print("=== Redis连接测试 ===")
    
    # 检查环境变量
    print(f"REDIS_HOST: {os.getenv('REDIS_HOST', 'localhost')}")
    print(f"REDIS_PORT: {os.getenv('REDIS_PORT', '6379')}")
    print(f"REDIS_DB: {os.getenv('REDIS_DB', '0')}")
    print(f"REDIS_PASSWORD: {os.getenv('REDIS_PASSWORD', 'None')}")
    
    # 检查连接状态
    is_connected = cache_manager.is_connected()
    print(f"Redis连接状态: {'✅ 已连接' if is_connected else '❌ 未连接'}")
    
    if cache_manager.redis_client:
        print(f"Redis客户端: {cache_manager.redis_client}")
        print(f"主机: {cache_manager.host}:{cache_manager.port}")
        print(f"数据库: {cache_manager.db}")
    else:
        print("Redis客户端: None (将使用内存缓存)")
    
    return is_connected

def test_cache_operations():
    """测试缓存操作"""
    print("\n=== 缓存操作测试 ===")
    
    # 测试数据
    test_keyword = "测试电影"
    test_results = [
        {
            'title': '测试电影1',
            'url': 'https://movie.douban.com/subject/1',
            'content': '这是一个测试电影',
            'source': '豆瓣电影',
            'keyword': test_keyword,
            'crawl_time': '2023-12-01 10:00:00'
        },
        {
            'title': '测试电影2',
            'url': 'https://movie.douban.com/subject/2',
            'content': '这是另一个测试电影',
            'source': '豆瓣电影',
            'keyword': test_keyword,
            'crawl_time': '2023-12-01 10:00:00'
        }
    ]
    
    # 测试写入缓存
    print(f"测试写入缓存: {test_keyword}")
    success = cache_manager.set_search_results(test_keyword, test_results)
    print(f"写入结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 测试读取缓存
    print(f"测试读取缓存: {test_keyword}")
    cached_results = cache_manager.get_search_results(test_keyword)
    if cached_results:
        print(f"读取结果: ✅ 成功，获取到 {len(cached_results)} 条结果")
        print(f"第一条结果: {cached_results[0]['title']}")
    else:
        print("读取结果: ❌ 失败或无数据")
    
    # 测试任务状态缓存
    print(f"\n测试任务状态缓存")
    task_id = "test_task_123"
    task_info = {
        'task_id': task_id,
        'keyword': test_keyword,
        'source': 'douban',
        'status': 'completed',
        'start_time': '2023-12-01T10:00:00',
        'end_time': '2023-12-01T10:05:00',
        'results': test_results
    }
    
    # 写入任务状态
    task_success = cache_manager.set_task_status(task_id, task_info)
    print(f"任务状态写入: {'✅ 成功' if task_success else '❌ 失败'}")
    
    # 读取任务状态
    cached_task = cache_manager.get_task_status(task_id)
    if cached_task:
        print(f"任务状态读取: ✅ 成功，状态: {cached_task.get('status')}")
    else:
        print("任务状态读取: ❌ 失败或无数据")
    
    return success and task_success

def test_cache_info():
    """测试缓存信息"""
    print("\n=== 缓存信息测试 ===")
    
    cache_info = cache_manager.get_cache_info()
    print(f"缓存连接状态: {cache_info.get('connected')}")
    print(f"总键数: {cache_info.get('total_keys')}")
    print(f"搜索键数: {cache_info.get('search_keys')}")
    
    if cache_info.get('redis_info'):
        redis_info = cache_info['redis_info']
        print(f"Redis版本: {redis_info.get('version')}")
        print(f"内存使用: {redis_info.get('used_memory_human')}")
    
    # 获取所有任务
    all_tasks = cache_manager.get_all_tasks()
    print(f"缓存中的任务数: {len(all_tasks)}")

def main():
    """主测试函数"""
    print("Redis缓存功能测试")
    print("=" * 50)
    
    # 测试连接
    connected = test_redis_connection()
    
    # 测试缓存操作
    cache_success = test_cache_operations()
    
    # 测试缓存信息
    test_cache_info()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"Redis连接: {'✅ 正常' if connected else '❌ 失败'}")
    print(f"缓存操作: {'✅ 正常' if cache_success else '❌ 失败'}")
    
    if not connected:
        print("\n解决方案:")
        print("1. 启动Redis服务器: redis-server")
        print("2. 或使用Docker: docker run -d -p 6379:6379 redis:latest")
        print("3. 检查Redis配置和端口")
        print("4. 如果没有Redis，系统会使用内存缓存（功能受限）")

if __name__ == "__main__":
    main()
