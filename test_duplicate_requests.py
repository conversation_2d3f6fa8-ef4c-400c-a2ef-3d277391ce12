#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import threading

# API基础URL
BASE_URL = "http://localhost:10080"

def test_duplicate_request_handling():
    """测试重复请求处理"""
    print("=== 测试重复请求处理 ===")
    
    # 首先添加一个测试数据源
    print("1. 添加测试数据源...")
    source_data = {
        "key": "test_source",
        "url": "https://httpbin.org/html",  # 简单的测试页面
        "ttl": 3600
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/source/add", json=source_data, timeout=30)
        if response.status_code == 200:
            print("✅ 测试数据源添加成功")
        else:
            print(f"❌ 添加数据源失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 添加数据源失败: {e}")
        return
    
    # 等待数据源处理完成
    time.sleep(3)
    
    # 测试搜索请求
    search_data = {
        "keyword": "test",
        "rule_key": "test_source"
    }
    
    print("\n2. 发送第一个搜索请求...")
    try:
        response1 = requests.post(f"{BASE_URL}/api/search/rule", json=search_data, timeout=15)
        if response1.status_code == 200:
            result1 = response1.json()
            print(f"✅ 第一个请求成功")
            print(f"   任务ID: {result1.get('task_id')}")
            print(f"   消息: {result1.get('message')}")
            print(f"   状态: {result1.get('status')}")
            print(f"   来源缓存: {result1.get('from_cache', False)}")
        else:
            print(f"❌ 第一个请求失败: {response1.status_code}")
            return
    except Exception as e:
        print(f"❌ 第一个请求失败: {e}")
        return
    
    print("\n3. 立即发送第二个相同的搜索请求...")
    try:
        response2 = requests.post(f"{BASE_URL}/api/search/rule", json=search_data, timeout=15)
        if response2.status_code == 200:
            result2 = response2.json()
            print(f"✅ 第二个请求成功")
            print(f"   任务ID: {result2.get('task_id')}")
            print(f"   消息: {result2.get('message')}")
            print(f"   状态: {result2.get('status')}")
            print(f"   来源缓存: {result2.get('from_cache', False)}")
            print(f"   是否重复请求: {result2.get('is_duplicate_request', False)}")
        else:
            print(f"❌ 第二个请求失败: {response2.status_code}")
    except Exception as e:
        print(f"❌ 第二个请求失败: {e}")
    
    print("\n4. 等待10秒后再次请求...")
    time.sleep(10)
    
    try:
        response3 = requests.post(f"{BASE_URL}/api/search/rule", json=search_data, timeout=15)
        if response3.status_code == 200:
            result3 = response3.json()
            print(f"✅ 第三个请求成功")
            print(f"   任务ID: {result3.get('task_id')}")
            print(f"   消息: {result3.get('message')}")
            print(f"   状态: {result3.get('status')}")
            print(f"   来源缓存: {result3.get('from_cache', False)}")
            if result3.get('status') == 'completed':
                print(f"   结果数量: {result3.get('results_count', 0)}")
        else:
            print(f"❌ 第三个请求失败: {response3.status_code}")
    except Exception as e:
        print(f"❌ 第三个请求失败: {e}")
    
    print("\n5. 测试强制刷新...")
    search_data_refresh = {
        "keyword": "test",
        "rule_key": "test_source",
        "force_refresh": True
    }
    
    try:
        response4 = requests.post(f"{BASE_URL}/api/search/rule", json=search_data_refresh, timeout=15)
        if response4.status_code == 200:
            result4 = response4.json()
            print(f"✅ 强制刷新请求成功")
            print(f"   任务ID: {result4.get('task_id')}")
            print(f"   消息: {result4.get('message')}")
            print(f"   状态: {result4.get('status')}")
            print(f"   来源缓存: {result4.get('from_cache', False)}")
        else:
            print(f"❌ 强制刷新请求失败: {response4.status_code}")
    except Exception as e:
        print(f"❌ 强制刷新请求失败: {e}")

def test_concurrent_requests():
    """测试并发请求"""
    print("\n=== 测试并发请求 ===")
    
    search_data = {
        "keyword": "concurrent_test",
        "rule_key": "test_source"
    }
    
    results = []
    
    def make_request(request_id):
        try:
            response = requests.post(f"{BASE_URL}/api/search/rule", json=search_data, timeout=15)
            if response.status_code == 200:
                result = response.json()
                results.append({
                    'request_id': request_id,
                    'task_id': result.get('task_id'),
                    'message': result.get('message'),
                    'status': result.get('status'),
                    'from_cache': result.get('from_cache', False),
                    'is_duplicate': result.get('is_duplicate_request', False)
                })
            else:
                results.append({
                    'request_id': request_id,
                    'error': f"HTTP {response.status_code}"
                })
        except Exception as e:
            results.append({
                'request_id': request_id,
                'error': str(e)
            })
    
    # 启动5个并发请求
    threads = []
    for i in range(5):
        thread = threading.Thread(target=make_request, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # 等待所有请求完成
    for thread in threads:
        thread.join()
    
    print(f"并发请求完成，共 {len(results)} 个响应:")
    for result in results:
        if 'error' in result:
            print(f"  请求 {result['request_id']}: ❌ {result['error']}")
        else:
            print(f"  请求 {result['request_id']}: ✅ {result['message'][:50]}...")
            print(f"    状态: {result['status']}, 缓存: {result['from_cache']}, 重复: {result['is_duplicate']}")

def main():
    """主测试函数"""
    print("开始测试重复请求处理...")
    print("=" * 60)
    
    # 健康检查
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器健康检查通过")
        else:
            print("❌ 服务器健康检查失败")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 测试重复请求处理
    test_duplicate_request_handling()
    
    # 测试并发请求
    test_concurrent_requests()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("预期行为:")
    print("1. 第一个请求启动任务")
    print("2. 第二个相同请求返回'任务进行中'")
    print("3. 任务完成后的请求返回缓存结果")
    print("4. 强制刷新会启动新任务")
    print("5. 并发请求中只有一个启动任务，其他返回重复请求提示")

if __name__ == "__main__":
    main()
