# 豆瓣电影关键字搜索爬虫系统

这是一个基于Scrapy和Flask的关键字搜索爬虫系统，专门用于从豆瓣电影网站爬取电影搜索结果。

## 功能特性

- 🎬 专门针对豆瓣电影网站的搜索
- 🕷️ 使用BeautifulSoup进行数据提取
- 🚀 使用Selenium处理JavaScript动态加载
- 🌐 提供REST API接口
- 📊 支持JSON和Excel格式输出
- ⚡ 异步任务处理
- 📝 详细的任务状态跟踪
- 🗄️ Redis缓存支持（任务状态和结果都存储在缓存中）
- 🔧 自动缓存管理（默认2小时TTL）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 环境配置

1. **Redis配置**（可选，如果不配置将跳过缓存功能）：
```bash
# 启动Redis服务器
redis-server

# 或使用Docker
docker run -d -p 6379:6379 redis:latest
```

2. **环境变量配置**：
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

配置示例：
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
DEFAULT_CACHE_TTL=7200
```

## 使用方法

### 1. 启动API服务器

```bash
python api_server.py
```

服务器将在 `http://localhost:10080` 启动。

### 2. 使用API接口

#### 启动搜索任务

```bash
curl -X POST http://localhost:5000/api/search \
  -H "Content-Type: application/json" \
  -d '{"keyword": "复仇者联盟", "use_cache": true}'
```

响应（如果有缓存）：
```json
{
  "task_id": "复仇者联盟_cached_1703123456",
  "message": "从缓存获取结果",
  "keyword": "复仇者联盟",
  "source": "douban",
  "status": "completed",
  "results": [...],
  "results_count": 15,
  "from_cache": true
}
```

响应（如果没有缓存）：
```json
{
  "task_id": "复仇者联盟_douban_1703123456",
  "message": "爬虫任务已启动",
  "keyword": "复仇者联盟",
  "source": "douban",
  "from_cache": false
}
```

#### 查询任务状态

```bash
curl http://localhost:5000/api/task/复仇者联盟_douban_1703123456
```

#### 查看所有任务

```bash
curl http://localhost:5000/api/tasks
```

### 3. 直接使用Scrapy命令

```bash
# 搜索豆瓣电影
scrapy crawl keyword_search -a keyword="复仇者联盟"

# 搜索其他电影
scrapy crawl keyword_search -a keyword="肖申克的救赎"
```

## API接口说明

### POST /api/search
启动关键字搜索任务

**请求参数：**
- `keyword` (string, 必需): 搜索关键字（电影名称）
- `use_cache` (boolean, 可选): 是否使用缓存，默认为 true

**响应：**
```json
{
  "task_id": "任务ID",
  "message": "爬虫任务已启动",
  "keyword": "搜索关键字",
  "source": "搜索源"
}
```

### GET /api/task/{task_id}
获取任务状态和结果

**响应：**
```json
{
  "task_id": "任务ID",
  "keyword": "搜索关键字",
  "source": "搜索源",
  "status": "running|completed|failed",
  "start_time": "开始时间",
  "end_time": "结束时间",
  "results_count": 结果数量,
  "results": [搜索结果数组]
}
```

### GET /api/tasks
获取所有任务列表

### GET /api/health
健康检查接口

## 缓存管理API

### GET /api/cache/info
获取缓存系统信息

**响应：**
```json
{
  "connected": true,
  "total_keys": 10,
  "search_keys": 5,
  "redis_info": {
    "version": "6.2.6",
    "used_memory_human": "1.2M"
  }
}
```

### GET /api/cache/keywords
获取所有缓存的关键字列表

**响应：**
```json
{
  "keywords": [
    {
      "keyword": "复仇者联盟",
      "source": "douban",
      "count": 15,
      "cached_at": "2023-12-01T10:30:00",
      "ttl": 3600
    }
  ],
  "count": 1
}
```

### DELETE /api/cache/clear
清空所有缓存

### DELETE /api/cache/delete/{keyword}
删除指定关键字的缓存

## 数据存储

爬取的结果直接存储在Redis缓存中：
- **缓存键**: 以关键字作为key
- **缓存值**: 搜索结果数组
- **缓存时间**: 默认2小时TTL
- **不再生成本地文件**: 所有数据通过API和缓存系统管理

## 数据结构

每个搜索结果包含以下字段：
- `title`: 标题
- `url`: 链接
- `content`: 内容摘要
- `source`: 来源网站
- `keyword`: 搜索关键字
- `crawl_time`: 爬取时间
- `publish_time`: 发布时间（如果可获取）

## 注意事项

1. 请遵守各搜索引擎的robots.txt规则
2. 建议设置合理的请求延迟，避免被反爬
3. 某些搜索引擎可能需要处理验证码或IP限制
4. 建议使用代理池来提高爬取成功率

## 配置说明

可以在 `news/settings.py` 中调整以下配置：
- `DOWNLOAD_DELAY`: 请求延迟
- `CONCURRENT_REQUESTS`: 并发请求数
- `USER_AGENT`: 用户代理
- `ROBOTSTXT_OBEY`: 是否遵守robots.txt

## 扩展功能

系统支持以下扩展：
1. 添加新的搜索引擎支持
2. 自定义数据提取规则
3. 添加数据清洗和去重功能
4. 集成数据库存储
5. 添加定时任务功能
